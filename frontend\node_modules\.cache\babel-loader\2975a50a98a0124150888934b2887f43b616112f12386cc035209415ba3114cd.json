{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserSettingsFixed.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaShieldAlt, FaEnvelope, FaBell, FaToggleOn, FaToggle<PERSON>ff, FaCheck, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';\nimport './UserSettings.css';\nimport User2FASetup from '../components/User/User2FASetup';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\nfunction UserSettings() {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showTfaSetup, setShowTfaSetup] = useState(false);\n\n  // User data\n  const [userId, setUserId] = useState(null);\n  const [userEmail, setUserEmail] = useState('');\n\n  // Security settings\n  const [securitySettings, setSecuritySettings] = useState({\n    otp_enabled: false,\n    tfa_enabled: false,\n    auth_method: 'password_only'\n  });\n\n  // Account settings\n  const [accountSettings, setAccountSettings] = useState({\n    email_notifications: true,\n    security_alerts: true,\n    login_notifications: true\n  });\n  useEffect(() => {\n    const storedUserId = localStorage.getItem('userId');\n    if (!storedUserId) {\n      navigate('/user/login');\n      return;\n    }\n    setUserId(storedUserId);\n    loadUserSettings();\n  }, [navigate]);\n  const loadUserSettings = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const userId = localStorage.getItem('userId');\n\n      // Load user security settings\n      const securityResponse = await axios.get(`${API_BASE_URL}/handlers/user_security_settings.php?userId=${userId}`);\n      if (securityResponse.data.success) {\n        setSecuritySettings(securityResponse.data.settings);\n        setUserEmail(securityResponse.data.user_email);\n      }\n\n      // Load account preferences\n      const accountResponse = await axios.get(`${API_BASE_URL}/handlers/user_account_settings.php?userId=${userId}`);\n      if (accountResponse.data.success) {\n        setAccountSettings(accountResponse.data.settings);\n      }\n    } catch (err) {\n      console.error('Settings load error:', err);\n      setError('Failed to load settings. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleOtpToggle = async () => {\n    try {\n      setError('');\n      const newOtpState = !securitySettings.otp_enabled;\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_toggle_otp.php`, {\n        userId: userId,\n        enabled: newOtpState\n      });\n      if (response.data.success) {\n        setSecuritySettings(prev => ({\n          ...prev,\n          otp_enabled: newOtpState\n        }));\n        setSuccess(`OTP ${newOtpState ? 'enabled' : 'disabled'} successfully`);\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update OTP setting');\n      }\n    } catch (err) {\n      setError('Failed to update OTP setting. Please try again.');\n    }\n  };\n  const handleAccountSettingToggle = async settingName => {\n    try {\n      setError('');\n      const newValue = !accountSettings[settingName];\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_account_settings.php`, {\n        userId: userId,\n        setting: settingName,\n        value: newValue\n      });\n      if (response.data.success) {\n        setAccountSettings(prev => ({\n          ...prev,\n          [settingName]: newValue\n        }));\n        setSuccess('Setting updated successfully');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update setting');\n      }\n    } catch (err) {\n      setError('Failed to update setting. Please try again.');\n    }\n  };\n  const handle2FASetup = () => {\n    setShowTfaSetup(true);\n  };\n  const on2FASetupComplete = () => {\n    setShowTfaSetup(false);\n    loadUserSettings(); // Reload settings to reflect changes\n    setSuccess('2FA has been successfully enabled!');\n    setTimeout(() => setSuccess(''), 3000);\n  };\n  const handle2FADisable = async () => {\n    if (!window.confirm('Are you sure you want to disable 2FA? This will reduce your account security.')) {\n      return;\n    }\n    try {\n      setError('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_disable_2fa.php`, {\n        userId: userId\n      });\n      if (response.data.success) {\n        setSecuritySettings(prev => ({\n          ...prev,\n          tfa_enabled: false\n        }));\n        setSuccess('2FA has been disabled');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to disable 2FA');\n      }\n    } catch (err) {\n      setError('Failed to disable 2FA. Please try again.');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-settings-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading settings...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 13\n    }, this);\n  }\n  if (showTfaSetup) {\n    return /*#__PURE__*/_jsxDEV(User2FASetup, {\n      userId: userId,\n      userEmail: userEmail,\n      onSuccess: on2FASetupComplete,\n      onBack: () => setShowTfaSetup(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-settings\",\n    children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-alert settings-alert-error\",\n      children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-alert settings-alert-success\",\n      children: [/*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 29\n            }, this), \" Security Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Configure two-factor authentication and security options\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-title\",\n                children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                  className: \"setting-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 37\n                }, this), \"One-Time Password (OTP)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-description\",\n                children: \"Receive a verification code via email when logging in\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-status\",\n                children: [\"Status: \", securitySettings.otp_enabled ? '✅ Enabled' : '❌ Disabled']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-control\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `toggle-btn ${securitySettings.otp_enabled ? 'active' : ''}`,\n                onClick: handleOtpToggle,\n                children: securitySettings.otp_enabled ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 69\n                }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 86\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-title\",\n                children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n                  className: \"setting-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 37\n                }, this), \"Two-Factor Authentication (2FA)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-description\",\n                children: \"Use Google Authenticator or similar app for enhanced security\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-status\",\n                children: [\"Status: \", securitySettings.tfa_enabled ? '✅ Enabled' : '❌ Disabled']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-control\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-secondary\",\n                disabled: true,\n                children: \"Setup 2FA (Coming Soon)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(FaBell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 29\n            }, this), \" Notification Preferences\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Choose what notifications you want to receive\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-title\",\n                children: \"Email Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-description\",\n                children: \"Receive general notifications via email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-control\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `toggle-btn ${accountSettings.email_notifications ? 'active' : ''}`,\n                onClick: () => handleAccountSettingToggle('email_notifications'),\n                children: accountSettings.email_notifications ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 76\n                }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 93\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-title\",\n                children: \"Security Alerts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-description\",\n                children: \"Get notified about security-related activities\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-control\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `toggle-btn ${accountSettings.security_alerts ? 'active' : ''}`,\n                onClick: () => handleAccountSettingToggle('security_alerts'),\n                children: accountSettings.security_alerts ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 72\n                }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 89\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-title\",\n                children: \"Login Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-description\",\n                children: \"Get notified when someone logs into your account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-control\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `toggle-btn ${accountSettings.login_notifications ? 'active' : ''}`,\n                onClick: () => handleAccountSettingToggle('login_notifications'),\n                children: accountSettings.login_notifications ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 76\n                }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 93\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 29\n            }, this), \" Debug Information\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Current settings and user information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"debug-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"User ID:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 32\n              }, this), \" \", userId]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Email:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 32\n              }, this), \" \", userEmail]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Auth Method:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 32\n              }, this), \" \", securitySettings.auth_method]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"OTP Enabled:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 32\n              }, this), \" \", securitySettings.otp_enabled ? 'Yes' : 'No']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"2FA Enabled:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 32\n              }, this), \" \", securitySettings.tfa_enabled ? 'Yes' : 'No']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 207,\n    columnNumber: 9\n  }, this);\n}\n_s(UserSettings, \"dKPPJEKFmJUAyHuWn2DxgMamB5s=\", false, function () {\n  return [useNavigate];\n});\n_c = UserSettings;\nexport default UserSettings;\nvar _c;\n$RefreshReg$(_c, \"UserSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "axios", "FaShieldAlt", "FaEnvelope", "FaBell", "FaToggleOn", "FaToggleOff", "FaCheck", "FaExclamationTriangle", "FaInfoCircle", "User2FASetup", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "UserSettings", "_s", "navigate", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showTfaSetup", "setShowTfaSetup", "userId", "setUserId", "userEmail", "setUserEmail", "securitySettings", "setSecuritySettings", "otp_enabled", "tfa_enabled", "auth_method", "accountSettings", "setAccountSettings", "email_notifications", "security_alerts", "login_notifications", "storedUserId", "localStorage", "getItem", "loadUserSettings", "securityResponse", "get", "data", "settings", "user_email", "accountResponse", "err", "console", "handleOtpToggle", "newOtpState", "response", "post", "enabled", "prev", "setTimeout", "message", "handleAccountSettingToggle", "<PERSON><PERSON><PERSON>", "newValue", "setting", "value", "handle2FASetup", "on2FASetupComplete", "handle2FADisable", "window", "confirm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSuccess", "onBack", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserSettingsFixed.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { \n    FaShieldAlt, \n    FaEnvelope, \n    FaBell, \n    FaToggleOn, \n    Fa<PERSON>og<PERSON><PERSON>ff,\n    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n    FaExclamation<PERSON><PERSON>gle,\n    FaInfoCircle\n} from 'react-icons/fa';\nimport './UserSettings.css';\nimport User2FASetup from '../components/User/User2FASetup';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\n\nfunction UserSettings() {\n    const navigate = useNavigate();\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [showTfaSetup, setShowTfaSetup] = useState(false);\n\n    // User data\n    const [userId, setUserId] = useState(null);\n    const [userEmail, setUserEmail] = useState('');\n    \n    // Security settings\n    const [securitySettings, setSecuritySettings] = useState({\n        otp_enabled: false,\n        tfa_enabled: false,\n        auth_method: 'password_only'\n    });\n    \n    // Account settings\n    const [accountSettings, setAccountSettings] = useState({\n        email_notifications: true,\n        security_alerts: true,\n        login_notifications: true\n    });\n\n    useEffect(() => {\n        const storedUserId = localStorage.getItem('userId');\n        if (!storedUserId) {\n            navigate('/user/login');\n            return;\n        }\n        \n        setUserId(storedUserId);\n        loadUserSettings();\n    }, [navigate]);\n\n    const loadUserSettings = async () => {\n        try {\n            setLoading(true);\n            setError('');\n\n            const userId = localStorage.getItem('userId');\n            \n            // Load user security settings\n            const securityResponse = await axios.get(\n                `${API_BASE_URL}/handlers/user_security_settings.php?userId=${userId}`\n            );\n\n            if (securityResponse.data.success) {\n                setSecuritySettings(securityResponse.data.settings);\n                setUserEmail(securityResponse.data.user_email);\n            }\n\n            // Load account preferences\n            const accountResponse = await axios.get(\n                `${API_BASE_URL}/handlers/user_account_settings.php?userId=${userId}`\n            );\n\n            if (accountResponse.data.success) {\n                setAccountSettings(accountResponse.data.settings);\n            }\n\n        } catch (err) {\n            console.error('Settings load error:', err);\n            setError('Failed to load settings. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleOtpToggle = async () => {\n        try {\n            setError('');\n            const newOtpState = !securitySettings.otp_enabled;\n            \n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/user_toggle_otp.php`,\n                {\n                    userId: userId,\n                    enabled: newOtpState\n                }\n            );\n\n            if (response.data.success) {\n                setSecuritySettings(prev => ({\n                    ...prev,\n                    otp_enabled: newOtpState\n                }));\n                setSuccess(`OTP ${newOtpState ? 'enabled' : 'disabled'} successfully`);\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to update OTP setting');\n            }\n        } catch (err) {\n            setError('Failed to update OTP setting. Please try again.');\n        }\n    };\n\n    const handleAccountSettingToggle = async (settingName) => {\n        try {\n            setError('');\n            const newValue = !accountSettings[settingName];\n            \n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/user_account_settings.php`,\n                {\n                    userId: userId,\n                    setting: settingName,\n                    value: newValue\n                }\n            );\n\n            if (response.data.success) {\n                setAccountSettings(prev => ({\n                    ...prev,\n                    [settingName]: newValue\n                }));\n                setSuccess('Setting updated successfully');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to update setting');\n            }\n        } catch (err) {\n            setError('Failed to update setting. Please try again.');\n        }\n    };\n\n    const handle2FASetup = () => {\n        setShowTfaSetup(true);\n    };\n\n    const on2FASetupComplete = () => {\n        setShowTfaSetup(false);\n        loadUserSettings(); // Reload settings to reflect changes\n        setSuccess('2FA has been successfully enabled!');\n        setTimeout(() => setSuccess(''), 3000);\n    };\n\n    const handle2FADisable = async () => {\n        if (!window.confirm('Are you sure you want to disable 2FA? This will reduce your account security.')) {\n            return;\n        }\n\n        try {\n            setError('');\n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/user_disable_2fa.php`,\n                {\n                    userId: userId\n                }\n            );\n\n            if (response.data.success) {\n                setSecuritySettings(prev => ({\n                    ...prev,\n                    tfa_enabled: false\n                }));\n                setSuccess('2FA has been disabled');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to disable 2FA');\n            }\n        } catch (err) {\n            setError('Failed to disable 2FA. Please try again.');\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"user-settings-loading\">\n                <div className=\"loading-spinner\"></div>\n                <p>Loading settings...</p>\n            </div>\n        );\n    }\n\n    if (showTfaSetup) {\n        return (\n            <User2FASetup\n                userId={userId}\n                userEmail={userEmail}\n                onSuccess={on2FASetupComplete}\n                onBack={() => setShowTfaSetup(false)}\n            />\n        );\n    }\n\n    return (\n        <div className=\"user-settings\">\n            {error && (\n                <div className=\"settings-alert settings-alert-error\">\n                    <FaExclamationTriangle />\n                    <span>{error}</span>\n                </div>\n            )}\n\n            {success && (\n                <div className=\"settings-alert settings-alert-success\">\n                    <FaCheck />\n                    <span>{success}</span>\n                </div>\n            )}\n\n            <div className=\"settings-grid\">\n                {/* Security Settings */}\n                <div className=\"settings-card\">\n                    <div className=\"settings-card-header\">\n                        <h2><FaShieldAlt /> Security Settings</h2>\n                        <p>Configure two-factor authentication and security options</p>\n                    </div>\n\n                    <div className=\"settings-card-content\">\n                        {/* OTP Setting */}\n                        <div className=\"setting-item\">\n                            <div className=\"setting-info\">\n                                <div className=\"setting-title\">\n                                    <FaEnvelope className=\"setting-icon\" />\n                                    One-Time Password (OTP)\n                                </div>\n                                <div className=\"setting-description\">\n                                    Receive a verification code via email when logging in\n                                </div>\n                                <div className=\"setting-status\">\n                                    Status: {securitySettings.otp_enabled ? '✅ Enabled' : '❌ Disabled'}\n                                </div>\n                            </div>\n                            <div className=\"setting-control\">\n                                <button\n                                    className={`toggle-btn ${securitySettings.otp_enabled ? 'active' : ''}`}\n                                    onClick={handleOtpToggle}\n                                >\n                                    {securitySettings.otp_enabled ? <FaToggleOn /> : <FaToggleOff />}\n                                </button>\n                            </div>\n                        </div>\n\n                        {/* 2FA Setting */}\n                        <div className=\"setting-item\">\n                            <div className=\"setting-info\">\n                                <div className=\"setting-title\">\n                                    <FaShieldAlt className=\"setting-icon\" />\n                                    Two-Factor Authentication (2FA)\n                                </div>\n                                <div className=\"setting-description\">\n                                    Use Google Authenticator or similar app for enhanced security\n                                </div>\n                                <div className=\"setting-status\">\n                                    Status: {securitySettings.tfa_enabled ? '✅ Enabled' : '❌ Disabled'}\n                                </div>\n                            </div>\n                            <div className=\"setting-control\">\n                                <button\n                                    className=\"btn btn-secondary\"\n                                    disabled\n                                >\n                                    Setup 2FA (Coming Soon)\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Account Preferences */}\n                <div className=\"settings-card\">\n                    <div className=\"settings-card-header\">\n                        <h2><FaBell /> Notification Preferences</h2>\n                        <p>Choose what notifications you want to receive</p>\n                    </div>\n\n                    <div className=\"settings-card-content\">\n                        <div className=\"setting-item\">\n                            <div className=\"setting-info\">\n                                <div className=\"setting-title\">Email Notifications</div>\n                                <div className=\"setting-description\">\n                                    Receive general notifications via email\n                                </div>\n                            </div>\n                            <div className=\"setting-control\">\n                                <button\n                                    className={`toggle-btn ${accountSettings.email_notifications ? 'active' : ''}`}\n                                    onClick={() => handleAccountSettingToggle('email_notifications')}\n                                >\n                                    {accountSettings.email_notifications ? <FaToggleOn /> : <FaToggleOff />}\n                                </button>\n                            </div>\n                        </div>\n\n                        <div className=\"setting-item\">\n                            <div className=\"setting-info\">\n                                <div className=\"setting-title\">Security Alerts</div>\n                                <div className=\"setting-description\">\n                                    Get notified about security-related activities\n                                </div>\n                            </div>\n                            <div className=\"setting-control\">\n                                <button\n                                    className={`toggle-btn ${accountSettings.security_alerts ? 'active' : ''}`}\n                                    onClick={() => handleAccountSettingToggle('security_alerts')}\n                                >\n                                    {accountSettings.security_alerts ? <FaToggleOn /> : <FaToggleOff />}\n                                </button>\n                            </div>\n                        </div>\n\n                        <div className=\"setting-item\">\n                            <div className=\"setting-info\">\n                                <div className=\"setting-title\">Login Notifications</div>\n                                <div className=\"setting-description\">\n                                    Get notified when someone logs into your account\n                                </div>\n                            </div>\n                            <div className=\"setting-control\">\n                                <button\n                                    className={`toggle-btn ${accountSettings.login_notifications ? 'active' : ''}`}\n                                    onClick={() => handleAccountSettingToggle('login_notifications')}\n                                >\n                                    {accountSettings.login_notifications ? <FaToggleOn /> : <FaToggleOff />}\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Debug Info */}\n                <div className=\"settings-card\">\n                    <div className=\"settings-card-header\">\n                        <h2><FaInfoCircle /> Debug Information</h2>\n                        <p>Current settings and user information</p>\n                    </div>\n\n                    <div className=\"settings-card-content\">\n                        <div className=\"debug-info\">\n                            <p><strong>User ID:</strong> {userId}</p>\n                            <p><strong>Email:</strong> {userEmail}</p>\n                            <p><strong>Auth Method:</strong> {securitySettings.auth_method}</p>\n                            <p><strong>OTP Enabled:</strong> {securitySettings.otp_enabled ? 'Yes' : 'No'}</p>\n                            <p><strong>2FA Enabled:</strong> {securitySettings.tfa_enabled ? 'Yes' : 'No'}</p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n}\n\nexport default UserSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACIC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,OAAO,EACPC,qBAAqB,EACrBC,YAAY,QACT,gBAAgB;AACvB,OAAO,oBAAoB;AAC3B,OAAOC,YAAY,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,oCAAoC;AAE/F,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC;IACrDoC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC;IACnDyC,mBAAmB,EAAE,IAAI;IACzBC,eAAe,EAAE,IAAI;IACrBC,mBAAmB,EAAE;EACzB,CAAC,CAAC;EAEF1C,SAAS,CAAC,MAAM;IACZ,MAAM2C,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACnD,IAAI,CAACF,YAAY,EAAE;MACfvB,QAAQ,CAAC,aAAa,CAAC;MACvB;IACJ;IAEAU,SAAS,CAACa,YAAY,CAAC;IACvBG,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC1B,QAAQ,CAAC,CAAC;EAEd,MAAM0B,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACAxB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMK,MAAM,GAAGe,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;;MAE7C;MACA,MAAME,gBAAgB,GAAG,MAAM7C,KAAK,CAAC8C,GAAG,CACpC,GAAGlC,YAAY,+CAA+Ce,MAAM,EACxE,CAAC;MAED,IAAIkB,gBAAgB,CAACE,IAAI,CAACxB,OAAO,EAAE;QAC/BS,mBAAmB,CAACa,gBAAgB,CAACE,IAAI,CAACC,QAAQ,CAAC;QACnDlB,YAAY,CAACe,gBAAgB,CAACE,IAAI,CAACE,UAAU,CAAC;MAClD;;MAEA;MACA,MAAMC,eAAe,GAAG,MAAMlD,KAAK,CAAC8C,GAAG,CACnC,GAAGlC,YAAY,8CAA8Ce,MAAM,EACvE,CAAC;MAED,IAAIuB,eAAe,CAACH,IAAI,CAACxB,OAAO,EAAE;QAC9Bc,kBAAkB,CAACa,eAAe,CAACH,IAAI,CAACC,QAAQ,CAAC;MACrD;IAEJ,CAAC,CAAC,OAAOG,GAAG,EAAE;MACVC,OAAO,CAAC/B,KAAK,CAAC,sBAAsB,EAAE8B,GAAG,CAAC;MAC1C7B,QAAQ,CAAC,4CAA4C,CAAC;IAC1D,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMiC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACA/B,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMgC,WAAW,GAAG,CAACvB,gBAAgB,CAACE,WAAW;MAEjD,MAAMsB,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,IAAI,CAC7B,GAAG5C,YAAY,+BAA+B,EAC9C;QACIe,MAAM,EAAEA,MAAM;QACd8B,OAAO,EAAEH;MACb,CACJ,CAAC;MAED,IAAIC,QAAQ,CAACR,IAAI,CAACxB,OAAO,EAAE;QACvBS,mBAAmB,CAAC0B,IAAI,KAAK;UACzB,GAAGA,IAAI;UACPzB,WAAW,EAAEqB;QACjB,CAAC,CAAC,CAAC;QACH9B,UAAU,CAAC,OAAO8B,WAAW,GAAG,SAAS,GAAG,UAAU,eAAe,CAAC;QACtEK,UAAU,CAAC,MAAMnC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACiC,QAAQ,CAACR,IAAI,CAACa,OAAO,IAAI,8BAA8B,CAAC;MACrE;IACJ,CAAC,CAAC,OAAOT,GAAG,EAAE;MACV7B,QAAQ,CAAC,iDAAiD,CAAC;IAC/D;EACJ,CAAC;EAED,MAAMuC,0BAA0B,GAAG,MAAOC,WAAW,IAAK;IACtD,IAAI;MACAxC,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMyC,QAAQ,GAAG,CAAC3B,eAAe,CAAC0B,WAAW,CAAC;MAE9C,MAAMP,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,IAAI,CAC7B,GAAG5C,YAAY,qCAAqC,EACpD;QACIe,MAAM,EAAEA,MAAM;QACdqC,OAAO,EAAEF,WAAW;QACpBG,KAAK,EAAEF;MACX,CACJ,CAAC;MAED,IAAIR,QAAQ,CAACR,IAAI,CAACxB,OAAO,EAAE;QACvBc,kBAAkB,CAACqB,IAAI,KAAK;UACxB,GAAGA,IAAI;UACP,CAACI,WAAW,GAAGC;QACnB,CAAC,CAAC,CAAC;QACHvC,UAAU,CAAC,8BAA8B,CAAC;QAC1CmC,UAAU,CAAC,MAAMnC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACiC,QAAQ,CAACR,IAAI,CAACa,OAAO,IAAI,0BAA0B,CAAC;MACjE;IACJ,CAAC,CAAC,OAAOT,GAAG,EAAE;MACV7B,QAAQ,CAAC,6CAA6C,CAAC;IAC3D;EACJ,CAAC;EAED,MAAM4C,cAAc,GAAGA,CAAA,KAAM;IACzBxC,eAAe,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyC,kBAAkB,GAAGA,CAAA,KAAM;IAC7BzC,eAAe,CAAC,KAAK,CAAC;IACtBkB,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACpBpB,UAAU,CAAC,oCAAoC,CAAC;IAChDmC,UAAU,CAAC,MAAMnC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAC1C,CAAC;EAED,MAAM4C,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,+EAA+E,CAAC,EAAE;MAClG;IACJ;IAEA,IAAI;MACAhD,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMiC,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,IAAI,CAC7B,GAAG5C,YAAY,gCAAgC,EAC/C;QACIe,MAAM,EAAEA;MACZ,CACJ,CAAC;MAED,IAAI4B,QAAQ,CAACR,IAAI,CAACxB,OAAO,EAAE;QACvBS,mBAAmB,CAAC0B,IAAI,KAAK;UACzB,GAAGA,IAAI;UACPxB,WAAW,EAAE;QACjB,CAAC,CAAC,CAAC;QACHV,UAAU,CAAC,uBAAuB,CAAC;QACnCmC,UAAU,CAAC,MAAMnC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACiC,QAAQ,CAACR,IAAI,CAACa,OAAO,IAAI,uBAAuB,CAAC;MAC9D;IACJ,CAAC,CAAC,OAAOT,GAAG,EAAE;MACV7B,QAAQ,CAAC,0CAA0C,CAAC;IACxD;EACJ,CAAC;EAED,IAAIH,OAAO,EAAE;IACT,oBACIR,OAAA;MAAK4D,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAClC7D,OAAA;QAAK4D,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCjE,OAAA;QAAA6D,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAEd;EAEA,IAAInD,YAAY,EAAE;IACd,oBACId,OAAA,CAACF,YAAY;MACTkB,MAAM,EAAEA,MAAO;MACfE,SAAS,EAAEA,SAAU;MACrBgD,SAAS,EAAEV,kBAAmB;MAC9BW,MAAM,EAAEA,CAAA,KAAMpD,eAAe,CAAC,KAAK;IAAE;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAEV;EAEA,oBACIjE,OAAA;IAAK4D,SAAS,EAAC,eAAe;IAAAC,QAAA,GACzBnD,KAAK,iBACFV,OAAA;MAAK4D,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAChD7D,OAAA,CAACJ,qBAAqB;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzBjE,OAAA;QAAA6D,QAAA,EAAOnD;MAAK;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACR,EAEArD,OAAO,iBACJZ,OAAA;MAAK4D,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAClD7D,OAAA,CAACL,OAAO;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXjE,OAAA;QAAA6D,QAAA,EAAOjD;MAAO;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACR,eAEDjE,OAAA;MAAK4D,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAE1B7D,OAAA;QAAK4D,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1B7D,OAAA;UAAK4D,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjC7D,OAAA;YAAA6D,QAAA,gBAAI7D,OAAA,CAACV,WAAW;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAAkB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1CjE,OAAA;YAAA6D,QAAA,EAAG;UAAwD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eAENjE,OAAA;UAAK4D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAElC7D,OAAA;YAAK4D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB7D,OAAA;cAAK4D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzB7D,OAAA;gBAAK4D,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1B7D,OAAA,CAACT,UAAU;kBAACqE,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,2BAE3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNjE,OAAA;gBAAK4D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNjE,OAAA;gBAAK4D,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAAC,UACpB,EAACzC,gBAAgB,CAACE,WAAW,GAAG,WAAW,GAAG,YAAY;cAAA;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNjE,OAAA;cAAK4D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5B7D,OAAA;gBACI4D,SAAS,EAAE,cAAcxC,gBAAgB,CAACE,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACxE8C,OAAO,EAAE1B,eAAgB;gBAAAmB,QAAA,EAExBzC,gBAAgB,CAACE,WAAW,gBAAGtB,OAAA,CAACP,UAAU;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACN,WAAW;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNjE,OAAA;YAAK4D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB7D,OAAA;cAAK4D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzB7D,OAAA;gBAAK4D,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1B7D,OAAA,CAACV,WAAW;kBAACsE,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mCAE5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNjE,OAAA;gBAAK4D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNjE,OAAA;gBAAK4D,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAAC,UACpB,EAACzC,gBAAgB,CAACG,WAAW,GAAG,WAAW,GAAG,YAAY;cAAA;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNjE,OAAA;cAAK4D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5B7D,OAAA;gBACI4D,SAAS,EAAC,mBAAmB;gBAC7BS,QAAQ;gBAAAR,QAAA,EACX;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNjE,OAAA;QAAK4D,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1B7D,OAAA;UAAK4D,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjC7D,OAAA;YAAA6D,QAAA,gBAAI7D,OAAA,CAACR,MAAM;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAAyB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CjE,OAAA;YAAA6D,QAAA,EAAG;UAA6C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAENjE,OAAA;UAAK4D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAClC7D,OAAA;YAAK4D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB7D,OAAA;cAAK4D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzB7D,OAAA;gBAAK4D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxDjE,OAAA;gBAAK4D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNjE,OAAA;cAAK4D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5B7D,OAAA;gBACI4D,SAAS,EAAE,cAAcnC,eAAe,CAACE,mBAAmB,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAC/EyC,OAAO,EAAEA,CAAA,KAAMlB,0BAA0B,CAAC,qBAAqB,CAAE;gBAAAW,QAAA,EAEhEpC,eAAe,CAACE,mBAAmB,gBAAG3B,OAAA,CAACP,UAAU;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACN,WAAW;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENjE,OAAA;YAAK4D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB7D,OAAA;cAAK4D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzB7D,OAAA;gBAAK4D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpDjE,OAAA;gBAAK4D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNjE,OAAA;cAAK4D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5B7D,OAAA;gBACI4D,SAAS,EAAE,cAAcnC,eAAe,CAACG,eAAe,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAC3EwC,OAAO,EAAEA,CAAA,KAAMlB,0BAA0B,CAAC,iBAAiB,CAAE;gBAAAW,QAAA,EAE5DpC,eAAe,CAACG,eAAe,gBAAG5B,OAAA,CAACP,UAAU;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACN,WAAW;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENjE,OAAA;YAAK4D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB7D,OAAA;cAAK4D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzB7D,OAAA;gBAAK4D,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxDjE,OAAA;gBAAK4D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNjE,OAAA;cAAK4D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5B7D,OAAA;gBACI4D,SAAS,EAAE,cAAcnC,eAAe,CAACI,mBAAmB,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAC/EuC,OAAO,EAAEA,CAAA,KAAMlB,0BAA0B,CAAC,qBAAqB,CAAE;gBAAAW,QAAA,EAEhEpC,eAAe,CAACI,mBAAmB,gBAAG7B,OAAA,CAACP,UAAU;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACN,WAAW;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNjE,OAAA;QAAK4D,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1B7D,OAAA;UAAK4D,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjC7D,OAAA;YAAA6D,QAAA,gBAAI7D,OAAA,CAACH,YAAY;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAAkB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3CjE,OAAA;YAAA6D,QAAA,EAAG;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAENjE,OAAA;UAAK4D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAClC7D,OAAA;YAAK4D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB7D,OAAA;cAAA6D,QAAA,gBAAG7D,OAAA;gBAAA6D,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACjD,MAAM;YAAA;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzCjE,OAAA;cAAA6D,QAAA,gBAAG7D,OAAA;gBAAA6D,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC/C,SAAS;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1CjE,OAAA;cAAA6D,QAAA,gBAAG7D,OAAA;gBAAA6D,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7C,gBAAgB,CAACI,WAAW;YAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnEjE,OAAA;cAAA6D,QAAA,gBAAG7D,OAAA;gBAAA6D,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7C,gBAAgB,CAACE,WAAW,GAAG,KAAK,GAAG,IAAI;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClFjE,OAAA;cAAA6D,QAAA,gBAAG7D,OAAA;gBAAA6D,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7C,gBAAgB,CAACG,WAAW,GAAG,KAAK,GAAG,IAAI;YAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAAC3D,EAAA,CAvVQD,YAAY;EAAA,QACAjB,WAAW;AAAA;AAAAkF,EAAA,GADvBjE,YAAY;AAyVrB,eAAeA,YAAY;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}