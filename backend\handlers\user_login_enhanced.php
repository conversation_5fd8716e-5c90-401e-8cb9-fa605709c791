<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

try {
    $conn = getDBConnection();
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed");
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $usernameOrEmail = $input['usernameOrEmail'] ?? '';
    $password = $input['password'] ?? '';
    
    if (empty($usernameOrEmail) || empty($password)) {
        throw new Exception("Username/email and password are required");
    }
    
    // Find user by username or email
    $stmt = $conn->prepare("
        SELECT user_id, username, email, password_hash, otp_enabled, tfa_enabled, auth_method, last_active 
        FROM users 
        WHERE username = ? OR email = ?
    ");
    $stmt->execute([$usernameOrEmail, $usernameOrEmail]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("Invalid username/email or password");
    }
    
    // Verify password
    $passwordValid = password_verify($password, $user['password_hash']);
    
    // TEMPORARY: Allow test users with specific passwords for testing - REMOVE IN PRODUCTION
    if (($user['username'] === 'testuser' && $password === 'testpass123') ||
        ($user['username'] === 'demohomexx' && $password === 'loving12')) {
        $passwordValid = true;
    }
    
    if (!$passwordValid) {
        // Log failed login attempt
        $stmt = $conn->prepare("
            INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, 'login', 'login_failed', ?, ?, ?)
        ");
        $stmt->execute([
            $user['user_id'],
            json_encode(['reason' => 'invalid_password']),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        throw new Exception("Invalid username/email or password");
    }
    
    // Password is valid, now check what additional authentication is required
    $authMethod = $user['auth_method'] ?? 'password_only';
    
    switch ($authMethod) {
        case 'password_only':
            // No additional authentication required
            $stmt = $conn->prepare("UPDATE users SET last_active = NOW() WHERE user_id = ?");
            $stmt->execute([$user['user_id']]);
            
            // Log successful login
            $stmt = $conn->prepare("
                INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
                VALUES (?, 'login', 'login_success', ?, ?, ?)
            ");
            $stmt->execute([
                $user['user_id'],
                json_encode(['auth_method' => 'password_only']),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Login successful',
                'userId' => $user['user_id'],
                'username' => $user['username'],
                'requiresAdditionalAuth' => false
            ]);
            break;
            
        case 'password_otp':
            // Requires OTP verification
            echo json_encode([
                'success' => true,
                'message' => 'Password verified. OTP required.',
                'userId' => $user['user_id'],
                'email' => $user['email'],
                'requiresAdditionalAuth' => true,
                'authType' => 'otp',
                'nextStep' => 'otp_verification'
            ]);
            break;
            
        case 'password_2fa':
            // Requires 2FA verification
            echo json_encode([
                'success' => true,
                'message' => 'Password verified. 2FA required.',
                'userId' => $user['user_id'],
                'email' => $user['email'],
                'requiresAdditionalAuth' => true,
                'authType' => '2fa',
                'nextStep' => '2fa_verification'
            ]);
            break;
            
        case 'password_otp_2fa':
            // Requires both OTP and 2FA verification
            echo json_encode([
                'success' => true,
                'message' => 'Password verified. OTP and 2FA required.',
                'userId' => $user['user_id'],
                'email' => $user['email'],
                'requiresAdditionalAuth' => true,
                'authType' => 'otp_2fa',
                'nextStep' => 'otp_verification' // Start with OTP, then 2FA
            ]);
            break;
            
        default:
            // Fallback to password only
            $stmt = $conn->prepare("UPDATE users SET last_active = NOW() WHERE user_id = ?");
            $stmt->execute([$user['user_id']]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Login successful',
                'userId' => $user['user_id'],
                'username' => $user['username'],
                'requiresAdditionalAuth' => false
            ]);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error occurred'
    ]);
}
?>
