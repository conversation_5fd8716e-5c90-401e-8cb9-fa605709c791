import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { 
    FaShieldAlt, 
    <PERSON><PERSON><PERSON><PERSON>, 
    <PERSON>aEnvelope, 
    <PERSON><PERSON><PERSON>ser, 
    Fa<PERSON>ell, 
    FaToggleOn, 
    FaToggleOff,
    FaQrcode,
    FaCopy,
    FaCheck,
    FaExclamationTriangle,
    FaInfoCircle
} from 'react-icons/fa';
// import UserLayout from '../components/UserLayout'; // Not needed - UserLayout is provided by routing
// import User2FASetup from '../components/User/User2FASetup'; // Will implement later
import './UserSettings.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';

function UserSettings() {
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    
    // User data
    const [userId, setUserId] = useState(null);
    const [userEmail, setUserEmail] = useState('');
    
    // Security settings
    const [securitySettings, setSecuritySettings] = useState({
        otp_enabled: false,
        tfa_enabled: false,
        auth_method: 'password_only'
    });
    
    // 2FA setup state
    const [tfaSetup, setTfaSetup] = useState(null);
    const [showTfaSetup, setShowTfaSetup] = useState(false);
    
    // Account settings
    const [accountSettings, setAccountSettings] = useState({
        email_notifications: true,
        security_alerts: true,
        login_notifications: true
    });

    useEffect(() => {
        const storedUserId = localStorage.getItem('userId');
        if (!storedUserId) {
            navigate('/user/login');
            return;
        }
        
        setUserId(storedUserId);
        loadUserSettings();
    }, [navigate]);

    const loadUserSettings = async () => {
        try {
            setLoading(true);
            setError('');

            const userId = localStorage.getItem('userId');
            
            // Load user security settings
            const securityResponse = await axios.get(
                `${API_BASE_URL}/handlers/user_security_settings.php?userId=${userId}`
            );

            if (securityResponse.data.success) {
                setSecuritySettings(securityResponse.data.settings);
                setUserEmail(securityResponse.data.user_email);
                setTfaSetup(securityResponse.data.tfa_setup);
            }

            // Load account preferences
            const accountResponse = await axios.get(
                `${API_BASE_URL}/handlers/user_account_settings.php?userId=${userId}`
            );

            if (accountResponse.data.success) {
                setAccountSettings(accountResponse.data.settings);
            }

        } catch (err) {
            console.error('Settings load error:', err);
            setError('Failed to load settings. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleOtpToggle = async () => {
        try {
            setError('');
            const newOtpState = !securitySettings.otp_enabled;
            
            const response = await axios.post(
                `${API_BASE_URL}/handlers/user_toggle_otp.php`,
                {
                    userId: userId,
                    enabled: newOtpState
                }
            );

            if (response.data.success) {
                setSecuritySettings(prev => ({
                    ...prev,
                    otp_enabled: newOtpState
                }));
                setSuccess(`OTP ${newOtpState ? 'enabled' : 'disabled'} successfully`);
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to update OTP setting');
            }
        } catch (err) {
            setError('Failed to update OTP setting. Please try again.');
        }
    };

    const handle2FAToggle = async () => {
        try {
            setError('');
            
            if (!securitySettings.tfa_enabled) {
                // Enable 2FA - show setup
                setShowTfaSetup(true);
            } else {
                // Disable 2FA
                const response = await axios.post(
                    `${API_BASE_URL}/handlers/user_disable_2fa.php`,
                    { userId: userId }
                );

                if (response.data.success) {
                    setSecuritySettings(prev => ({
                        ...prev,
                        tfa_enabled: false
                    }));
                    setTfaSetup(null);
                    setSuccess('2FA disabled successfully');
                    setTimeout(() => setSuccess(''), 3000);
                } else {
                    setError(response.data.message || 'Failed to disable 2FA');
                }
            }
        } catch (err) {
            setError('Failed to update 2FA setting. Please try again.');
        }
    };

    const handleAccountSettingToggle = async (settingName) => {
        try {
            setError('');
            const newValue = !accountSettings[settingName];
            
            const response = await axios.post(
                `${API_BASE_URL}/handlers/user_account_settings.php`,
                {
                    userId: userId,
                    setting: settingName,
                    value: newValue
                }
            );

            if (response.data.success) {
                setAccountSettings(prev => ({
                    ...prev,
                    [settingName]: newValue
                }));
                setSuccess('Setting updated successfully');
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to update setting');
            }
        } catch (err) {
            setError('Failed to update setting. Please try again.');
        }
    };

    const on2FASetupComplete = () => {
        setShowTfaSetup(false);
        setSecuritySettings(prev => ({
            ...prev,
            tfa_enabled: true
        }));
        setSuccess('2FA setup completed successfully!');
        setTimeout(() => setSuccess(''), 3000);
        loadUserSettings(); // Reload to get updated setup info
    };

    if (loading) {
        return (
            <div className="user-settings-loading">
                <div className="loading-spinner"></div>
                <p>Loading settings...</p>
            </div>
        );
    }

    // if (showTfaSetup) {
    //     return (
    //         <User2FASetup
    //             userId={userId}
    //             userEmail={userEmail}
    //             onSuccess={on2FASetupComplete}
    //             onBack={() => setShowTfaSetup(false)}
    //         />
    //     );
    // }

    return (
        <div className="user-settings">

                {error && (
                    <div className="settings-alert settings-alert-error">
                        <FaExclamationTriangle />
                        <span>{error}</span>
                    </div>
                )}

                {success && (
                    <div className="settings-alert settings-alert-success">
                        <FaCheck />
                        <span>{success}</span>
                    </div>
                )}

                <div className="settings-grid">
                    {/* Security Settings */}
                    <div className="settings-card">
                        <div className="settings-card-header">
                            <h2><FaShieldAlt /> Security Settings</h2>
                            <p>Configure two-factor authentication and security options</p>
                        </div>

                        <div className="settings-card-content">
                            {/* OTP Setting */}
                            <div className="setting-item">
                                <div className="setting-info">
                                    <div className="setting-title">
                                        <FaEnvelope className="setting-icon" />
                                        One-Time Password (OTP)
                                    </div>
                                    <div className="setting-description">
                                        Receive a verification code via email when logging in
                                    </div>
                                </div>
                                <div className="setting-control">
                                    <button
                                        className={`toggle-btn ${securitySettings.otp_enabled ? 'active' : ''}`}
                                        onClick={handleOtpToggle}
                                    >
                                        {securitySettings.otp_enabled ? <FaToggleOn /> : <FaToggleOff />}
                                    </button>
                                </div>
                            </div>

                            {/* 2FA Setting */}
                            <div className="setting-item">
                                <div className="setting-info">
                                    <div className="setting-title">
                                        <FaQrcode className="setting-icon" />
                                        Two-Factor Authentication (2FA)
                                    </div>
                                    <div className="setting-description">
                                        Use Google Authenticator or similar app for enhanced security
                                    </div>
                                    {tfaSetup?.setup_completed && (
                                        <div className="setting-status">
                                            <FaCheck className="status-icon success" />
                                            2FA is configured and active
                                        </div>
                                    )}
                                </div>
                                <div className="setting-control">
                                    <button
                                        className={`toggle-btn ${securitySettings.tfa_enabled ? 'active' : ''}`}
                                        onClick={handle2FAToggle}
                                    >
                                        {securitySettings.tfa_enabled ? <FaToggleOn /> : <FaToggleOff />}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Account Preferences */}
                    <div className="settings-card">
                        <div className="settings-card-header">
                            <h2><FaBell /> Notification Preferences</h2>
                            <p>Choose what notifications you want to receive</p>
                        </div>

                        <div className="settings-card-content">
                            <div className="setting-item">
                                <div className="setting-info">
                                    <div className="setting-title">Email Notifications</div>
                                    <div className="setting-description">
                                        Receive general notifications via email
                                    </div>
                                </div>
                                <div className="setting-control">
                                    <button
                                        className={`toggle-btn ${accountSettings.email_notifications ? 'active' : ''}`}
                                        onClick={() => handleAccountSettingToggle('email_notifications')}
                                    >
                                        {accountSettings.email_notifications ? <FaToggleOn /> : <FaToggleOff />}
                                    </button>
                                </div>
                            </div>

                            <div className="setting-item">
                                <div className="setting-info">
                                    <div className="setting-title">Security Alerts</div>
                                    <div className="setting-description">
                                        Get notified about security-related activities
                                    </div>
                                </div>
                                <div className="setting-control">
                                    <button
                                        className={`toggle-btn ${accountSettings.security_alerts ? 'active' : ''}`}
                                        onClick={() => handleAccountSettingToggle('security_alerts')}
                                    >
                                        {accountSettings.security_alerts ? <FaToggleOn /> : <FaToggleOff />}
                                    </button>
                                </div>
                            </div>

                            <div className="setting-item">
                                <div className="setting-info">
                                    <div className="setting-title">Login Notifications</div>
                                    <div className="setting-description">
                                        Get notified when someone logs into your account
                                    </div>
                                </div>
                                <div className="setting-control">
                                    <button
                                        className={`toggle-btn ${accountSettings.login_notifications ? 'active' : ''}`}
                                        onClick={() => handleAccountSettingToggle('login_notifications')}
                                    >
                                        {accountSettings.login_notifications ? <FaToggleOn /> : <FaToggleOff />}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Security Info */}
                    <div className="settings-card">
                        <div className="settings-card-header">
                            <h2><FaInfoCircle /> Security Information</h2>
                            <p>Important security recommendations</p>
                        </div>

                        <div className="settings-card-content">
                            <div className="security-tips">
                                <div className="security-tip">
                                    <FaShieldAlt className="tip-icon" />
                                    <div>
                                        <strong>Enable 2FA:</strong> Two-factor authentication provides an extra layer of security for your account.
                                    </div>
                                </div>
                                <div className="security-tip">
                                    <FaEnvelope className="tip-icon" />
                                    <div>
                                        <strong>OTP Verification:</strong> Email OTP helps verify your identity during login attempts.
                                    </div>
                                </div>
                                <div className="security-tip">
                                    <FaKey className="tip-icon" />
                                    <div>
                                        <strong>Strong Password:</strong> Use a unique, strong password for your FanBet247 account.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default UserSettings;
