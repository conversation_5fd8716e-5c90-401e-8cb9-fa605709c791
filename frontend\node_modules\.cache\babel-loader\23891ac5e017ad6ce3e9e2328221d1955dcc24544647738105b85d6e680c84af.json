{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserSettingsFixed.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaShieldAlt, FaEnvelope, FaBell, FaToggleOn, FaToggle<PERSON>ff, FaCheck, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';\nimport './UserSettings.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\nfunction UserSettings() {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // User data\n  const [userId, setUserId] = useState(null);\n  const [userEmail, setUserEmail] = useState('');\n\n  // Security settings\n  const [securitySettings, setSecuritySettings] = useState({\n    otp_enabled: false,\n    tfa_enabled: false,\n    auth_method: 'password_only'\n  });\n\n  // Account settings\n  const [accountSettings, setAccountSettings] = useState({\n    email_notifications: true,\n    security_alerts: true,\n    login_notifications: true\n  });\n  useEffect(() => {\n    const storedUserId = localStorage.getItem('userId');\n    if (!storedUserId) {\n      navigate('/user/login');\n      return;\n    }\n    setUserId(storedUserId);\n    loadUserSettings();\n  }, [navigate]);\n  const loadUserSettings = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const userId = localStorage.getItem('userId');\n\n      // Load user security settings\n      const securityResponse = await axios.get(`${API_BASE_URL}/handlers/user_security_settings.php?userId=${userId}`);\n      if (securityResponse.data.success) {\n        setSecuritySettings(securityResponse.data.settings);\n        setUserEmail(securityResponse.data.user_email);\n      }\n\n      // Load account preferences\n      const accountResponse = await axios.get(`${API_BASE_URL}/handlers/user_account_settings.php?userId=${userId}`);\n      if (accountResponse.data.success) {\n        setAccountSettings(accountResponse.data.settings);\n      }\n    } catch (err) {\n      console.error('Settings load error:', err);\n      setError('Failed to load settings. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleOtpToggle = async () => {\n    try {\n      setError('');\n      const newOtpState = !securitySettings.otp_enabled;\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_toggle_otp.php`, {\n        userId: userId,\n        enabled: newOtpState\n      });\n      if (response.data.success) {\n        setSecuritySettings(prev => ({\n          ...prev,\n          otp_enabled: newOtpState\n        }));\n        setSuccess(`OTP ${newOtpState ? 'enabled' : 'disabled'} successfully`);\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update OTP setting');\n      }\n    } catch (err) {\n      setError('Failed to update OTP setting. Please try again.');\n    }\n  };\n  const handleAccountSettingToggle = async settingName => {\n    try {\n      setError('');\n      const newValue = !accountSettings[settingName];\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_account_settings.php`, {\n        userId: userId,\n        setting: settingName,\n        value: newValue\n      });\n      if (response.data.success) {\n        setAccountSettings(prev => ({\n          ...prev,\n          [settingName]: newValue\n        }));\n        setSuccess('Setting updated successfully');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update setting');\n      }\n    } catch (err) {\n      setError('Failed to update setting. Please try again.');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-settings-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading settings...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-settings\",\n    children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-alert settings-alert-error\",\n      children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-alert settings-alert-success\",\n      children: [/*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 29\n            }, this), \" Security Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Configure two-factor authentication and security options\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-title\",\n                children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                  className: \"setting-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 37\n                }, this), \"One-Time Password (OTP)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-description\",\n                children: \"Receive a verification code via email when logging in\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-status\",\n                children: [\"Status: \", securitySettings.otp_enabled ? '✅ Enabled' : '❌ Disabled']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-control\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `toggle-btn ${securitySettings.otp_enabled ? 'active' : ''}`,\n                onClick: handleOtpToggle,\n                children: securitySettings.otp_enabled ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 69\n                }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 86\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-title\",\n                children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n                  className: \"setting-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 37\n                }, this), \"Two-Factor Authentication (2FA)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-description\",\n                children: \"Use Google Authenticator or similar app for enhanced security\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-status\",\n                children: [\"Status: \", securitySettings.tfa_enabled ? '✅ Enabled' : '❌ Disabled']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-control\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-secondary\",\n                disabled: true,\n                children: \"Setup 2FA (Coming Soon)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(FaBell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 29\n            }, this), \" Notification Preferences\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Choose what notifications you want to receive\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-title\",\n                children: \"Email Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-description\",\n                children: \"Receive general notifications via email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-control\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `toggle-btn ${accountSettings.email_notifications ? 'active' : ''}`,\n                onClick: () => handleAccountSettingToggle('email_notifications'),\n                children: accountSettings.email_notifications ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 76\n                }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 93\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-title\",\n                children: \"Security Alerts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-description\",\n                children: \"Get notified about security-related activities\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-control\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `toggle-btn ${accountSettings.security_alerts ? 'active' : ''}`,\n                onClick: () => handleAccountSettingToggle('security_alerts'),\n                children: accountSettings.security_alerts ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 72\n                }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 89\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-title\",\n                children: \"Login Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-description\",\n                children: \"Get notified when someone logs into your account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-control\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `toggle-btn ${accountSettings.login_notifications ? 'active' : ''}`,\n                onClick: () => handleAccountSettingToggle('login_notifications'),\n                children: accountSettings.login_notifications ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 76\n                }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 93\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 29\n            }, this), \" Debug Information\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Current settings and user information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"debug-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"User ID:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 32\n              }, this), \" \", userId]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Email:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 32\n              }, this), \" \", userEmail]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Auth Method:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 32\n              }, this), \" \", securitySettings.auth_method]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"OTP Enabled:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 32\n              }, this), \" \", securitySettings.otp_enabled ? 'Yes' : 'No']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"2FA Enabled:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 32\n              }, this), \" \", securitySettings.tfa_enabled ? 'Yes' : 'No']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 9\n  }, this);\n}\n_s(UserSettings, \"WEWuYuBDeM6K9l1p/NeXUXRdfms=\", false, function () {\n  return [useNavigate];\n});\n_c = UserSettings;\nexport default UserSettings;\nvar _c;\n$RefreshReg$(_c, \"UserSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "axios", "FaShieldAlt", "FaEnvelope", "FaBell", "FaToggleOn", "FaToggleOff", "FaCheck", "FaExclamationTriangle", "FaInfoCircle", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "UserSettings", "_s", "navigate", "loading", "setLoading", "error", "setError", "success", "setSuccess", "userId", "setUserId", "userEmail", "setUserEmail", "securitySettings", "setSecuritySettings", "otp_enabled", "tfa_enabled", "auth_method", "accountSettings", "setAccountSettings", "email_notifications", "security_alerts", "login_notifications", "storedUserId", "localStorage", "getItem", "loadUserSettings", "securityResponse", "get", "data", "settings", "user_email", "accountResponse", "err", "console", "handleOtpToggle", "newOtpState", "response", "post", "enabled", "prev", "setTimeout", "message", "handleAccountSettingToggle", "<PERSON><PERSON><PERSON>", "newValue", "setting", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserSettingsFixed.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { \n    FaShieldAlt, \n    FaEnvelope, \n    FaBell, \n    FaToggleOn, \n    FaToggle<PERSON>ff,\n    Fa<PERSON>heck,\n    FaExclamationTriangle,\n    FaInfoCircle\n} from 'react-icons/fa';\nimport './UserSettings.css';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\n\nfunction UserSettings() {\n    const navigate = useNavigate();\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    \n    // User data\n    const [userId, setUserId] = useState(null);\n    const [userEmail, setUserEmail] = useState('');\n    \n    // Security settings\n    const [securitySettings, setSecuritySettings] = useState({\n        otp_enabled: false,\n        tfa_enabled: false,\n        auth_method: 'password_only'\n    });\n    \n    // Account settings\n    const [accountSettings, setAccountSettings] = useState({\n        email_notifications: true,\n        security_alerts: true,\n        login_notifications: true\n    });\n\n    useEffect(() => {\n        const storedUserId = localStorage.getItem('userId');\n        if (!storedUserId) {\n            navigate('/user/login');\n            return;\n        }\n        \n        setUserId(storedUserId);\n        loadUserSettings();\n    }, [navigate]);\n\n    const loadUserSettings = async () => {\n        try {\n            setLoading(true);\n            setError('');\n\n            const userId = localStorage.getItem('userId');\n            \n            // Load user security settings\n            const securityResponse = await axios.get(\n                `${API_BASE_URL}/handlers/user_security_settings.php?userId=${userId}`\n            );\n\n            if (securityResponse.data.success) {\n                setSecuritySettings(securityResponse.data.settings);\n                setUserEmail(securityResponse.data.user_email);\n            }\n\n            // Load account preferences\n            const accountResponse = await axios.get(\n                `${API_BASE_URL}/handlers/user_account_settings.php?userId=${userId}`\n            );\n\n            if (accountResponse.data.success) {\n                setAccountSettings(accountResponse.data.settings);\n            }\n\n        } catch (err) {\n            console.error('Settings load error:', err);\n            setError('Failed to load settings. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleOtpToggle = async () => {\n        try {\n            setError('');\n            const newOtpState = !securitySettings.otp_enabled;\n            \n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/user_toggle_otp.php`,\n                {\n                    userId: userId,\n                    enabled: newOtpState\n                }\n            );\n\n            if (response.data.success) {\n                setSecuritySettings(prev => ({\n                    ...prev,\n                    otp_enabled: newOtpState\n                }));\n                setSuccess(`OTP ${newOtpState ? 'enabled' : 'disabled'} successfully`);\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to update OTP setting');\n            }\n        } catch (err) {\n            setError('Failed to update OTP setting. Please try again.');\n        }\n    };\n\n    const handleAccountSettingToggle = async (settingName) => {\n        try {\n            setError('');\n            const newValue = !accountSettings[settingName];\n            \n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/user_account_settings.php`,\n                {\n                    userId: userId,\n                    setting: settingName,\n                    value: newValue\n                }\n            );\n\n            if (response.data.success) {\n                setAccountSettings(prev => ({\n                    ...prev,\n                    [settingName]: newValue\n                }));\n                setSuccess('Setting updated successfully');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to update setting');\n            }\n        } catch (err) {\n            setError('Failed to update setting. Please try again.');\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"user-settings-loading\">\n                <div className=\"loading-spinner\"></div>\n                <p>Loading settings...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"user-settings\">\n            {error && (\n                <div className=\"settings-alert settings-alert-error\">\n                    <FaExclamationTriangle />\n                    <span>{error}</span>\n                </div>\n            )}\n\n            {success && (\n                <div className=\"settings-alert settings-alert-success\">\n                    <FaCheck />\n                    <span>{success}</span>\n                </div>\n            )}\n\n            <div className=\"settings-grid\">\n                {/* Security Settings */}\n                <div className=\"settings-card\">\n                    <div className=\"settings-card-header\">\n                        <h2><FaShieldAlt /> Security Settings</h2>\n                        <p>Configure two-factor authentication and security options</p>\n                    </div>\n\n                    <div className=\"settings-card-content\">\n                        {/* OTP Setting */}\n                        <div className=\"setting-item\">\n                            <div className=\"setting-info\">\n                                <div className=\"setting-title\">\n                                    <FaEnvelope className=\"setting-icon\" />\n                                    One-Time Password (OTP)\n                                </div>\n                                <div className=\"setting-description\">\n                                    Receive a verification code via email when logging in\n                                </div>\n                                <div className=\"setting-status\">\n                                    Status: {securitySettings.otp_enabled ? '✅ Enabled' : '❌ Disabled'}\n                                </div>\n                            </div>\n                            <div className=\"setting-control\">\n                                <button\n                                    className={`toggle-btn ${securitySettings.otp_enabled ? 'active' : ''}`}\n                                    onClick={handleOtpToggle}\n                                >\n                                    {securitySettings.otp_enabled ? <FaToggleOn /> : <FaToggleOff />}\n                                </button>\n                            </div>\n                        </div>\n\n                        {/* 2FA Setting */}\n                        <div className=\"setting-item\">\n                            <div className=\"setting-info\">\n                                <div className=\"setting-title\">\n                                    <FaShieldAlt className=\"setting-icon\" />\n                                    Two-Factor Authentication (2FA)\n                                </div>\n                                <div className=\"setting-description\">\n                                    Use Google Authenticator or similar app for enhanced security\n                                </div>\n                                <div className=\"setting-status\">\n                                    Status: {securitySettings.tfa_enabled ? '✅ Enabled' : '❌ Disabled'}\n                                </div>\n                            </div>\n                            <div className=\"setting-control\">\n                                <button\n                                    className=\"btn btn-secondary\"\n                                    disabled\n                                >\n                                    Setup 2FA (Coming Soon)\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Account Preferences */}\n                <div className=\"settings-card\">\n                    <div className=\"settings-card-header\">\n                        <h2><FaBell /> Notification Preferences</h2>\n                        <p>Choose what notifications you want to receive</p>\n                    </div>\n\n                    <div className=\"settings-card-content\">\n                        <div className=\"setting-item\">\n                            <div className=\"setting-info\">\n                                <div className=\"setting-title\">Email Notifications</div>\n                                <div className=\"setting-description\">\n                                    Receive general notifications via email\n                                </div>\n                            </div>\n                            <div className=\"setting-control\">\n                                <button\n                                    className={`toggle-btn ${accountSettings.email_notifications ? 'active' : ''}`}\n                                    onClick={() => handleAccountSettingToggle('email_notifications')}\n                                >\n                                    {accountSettings.email_notifications ? <FaToggleOn /> : <FaToggleOff />}\n                                </button>\n                            </div>\n                        </div>\n\n                        <div className=\"setting-item\">\n                            <div className=\"setting-info\">\n                                <div className=\"setting-title\">Security Alerts</div>\n                                <div className=\"setting-description\">\n                                    Get notified about security-related activities\n                                </div>\n                            </div>\n                            <div className=\"setting-control\">\n                                <button\n                                    className={`toggle-btn ${accountSettings.security_alerts ? 'active' : ''}`}\n                                    onClick={() => handleAccountSettingToggle('security_alerts')}\n                                >\n                                    {accountSettings.security_alerts ? <FaToggleOn /> : <FaToggleOff />}\n                                </button>\n                            </div>\n                        </div>\n\n                        <div className=\"setting-item\">\n                            <div className=\"setting-info\">\n                                <div className=\"setting-title\">Login Notifications</div>\n                                <div className=\"setting-description\">\n                                    Get notified when someone logs into your account\n                                </div>\n                            </div>\n                            <div className=\"setting-control\">\n                                <button\n                                    className={`toggle-btn ${accountSettings.login_notifications ? 'active' : ''}`}\n                                    onClick={() => handleAccountSettingToggle('login_notifications')}\n                                >\n                                    {accountSettings.login_notifications ? <FaToggleOn /> : <FaToggleOff />}\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                {/* Debug Info */}\n                <div className=\"settings-card\">\n                    <div className=\"settings-card-header\">\n                        <h2><FaInfoCircle /> Debug Information</h2>\n                        <p>Current settings and user information</p>\n                    </div>\n\n                    <div className=\"settings-card-content\">\n                        <div className=\"debug-info\">\n                            <p><strong>User ID:</strong> {userId}</p>\n                            <p><strong>Email:</strong> {userEmail}</p>\n                            <p><strong>Auth Method:</strong> {securitySettings.auth_method}</p>\n                            <p><strong>OTP Enabled:</strong> {securitySettings.otp_enabled ? 'Yes' : 'No'}</p>\n                            <p><strong>2FA Enabled:</strong> {securitySettings.tfa_enabled ? 'Yes' : 'No'}</p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n}\n\nexport default UserSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACIC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,OAAO,EACPC,qBAAqB,EACrBC,YAAY,QACT,gBAAgB;AACvB,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,oCAAoC;AAE/F,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC2B,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC;IACrDiC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC;IACnDsC,mBAAmB,EAAE,IAAI;IACzBC,eAAe,EAAE,IAAI;IACrBC,mBAAmB,EAAE;EACzB,CAAC,CAAC;EAEFvC,SAAS,CAAC,MAAM;IACZ,MAAMwC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACnD,IAAI,CAACF,YAAY,EAAE;MACfrB,QAAQ,CAAC,aAAa,CAAC;MACvB;IACJ;IAEAQ,SAAS,CAACa,YAAY,CAAC;IACvBG,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACxB,QAAQ,CAAC,CAAC;EAEd,MAAMwB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACAtB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMG,MAAM,GAAGe,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;;MAE7C;MACA,MAAME,gBAAgB,GAAG,MAAM1C,KAAK,CAAC2C,GAAG,CACpC,GAAGhC,YAAY,+CAA+Ca,MAAM,EACxE,CAAC;MAED,IAAIkB,gBAAgB,CAACE,IAAI,CAACtB,OAAO,EAAE;QAC/BO,mBAAmB,CAACa,gBAAgB,CAACE,IAAI,CAACC,QAAQ,CAAC;QACnDlB,YAAY,CAACe,gBAAgB,CAACE,IAAI,CAACE,UAAU,CAAC;MAClD;;MAEA;MACA,MAAMC,eAAe,GAAG,MAAM/C,KAAK,CAAC2C,GAAG,CACnC,GAAGhC,YAAY,8CAA8Ca,MAAM,EACvE,CAAC;MAED,IAAIuB,eAAe,CAACH,IAAI,CAACtB,OAAO,EAAE;QAC9BY,kBAAkB,CAACa,eAAe,CAACH,IAAI,CAACC,QAAQ,CAAC;MACrD;IAEJ,CAAC,CAAC,OAAOG,GAAG,EAAE;MACVC,OAAO,CAAC7B,KAAK,CAAC,sBAAsB,EAAE4B,GAAG,CAAC;MAC1C3B,QAAQ,CAAC,4CAA4C,CAAC;IAC1D,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM+B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACA7B,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAM8B,WAAW,GAAG,CAACvB,gBAAgB,CAACE,WAAW;MAEjD,MAAMsB,QAAQ,GAAG,MAAMpD,KAAK,CAACqD,IAAI,CAC7B,GAAG1C,YAAY,+BAA+B,EAC9C;QACIa,MAAM,EAAEA,MAAM;QACd8B,OAAO,EAAEH;MACb,CACJ,CAAC;MAED,IAAIC,QAAQ,CAACR,IAAI,CAACtB,OAAO,EAAE;QACvBO,mBAAmB,CAAC0B,IAAI,KAAK;UACzB,GAAGA,IAAI;UACPzB,WAAW,EAAEqB;QACjB,CAAC,CAAC,CAAC;QACH5B,UAAU,CAAC,OAAO4B,WAAW,GAAG,SAAS,GAAG,UAAU,eAAe,CAAC;QACtEK,UAAU,CAAC,MAAMjC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC+B,QAAQ,CAACR,IAAI,CAACa,OAAO,IAAI,8BAA8B,CAAC;MACrE;IACJ,CAAC,CAAC,OAAOT,GAAG,EAAE;MACV3B,QAAQ,CAAC,iDAAiD,CAAC;IAC/D;EACJ,CAAC;EAED,MAAMqC,0BAA0B,GAAG,MAAOC,WAAW,IAAK;IACtD,IAAI;MACAtC,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMuC,QAAQ,GAAG,CAAC3B,eAAe,CAAC0B,WAAW,CAAC;MAE9C,MAAMP,QAAQ,GAAG,MAAMpD,KAAK,CAACqD,IAAI,CAC7B,GAAG1C,YAAY,qCAAqC,EACpD;QACIa,MAAM,EAAEA,MAAM;QACdqC,OAAO,EAAEF,WAAW;QACpBG,KAAK,EAAEF;MACX,CACJ,CAAC;MAED,IAAIR,QAAQ,CAACR,IAAI,CAACtB,OAAO,EAAE;QACvBY,kBAAkB,CAACqB,IAAI,KAAK;UACxB,GAAGA,IAAI;UACP,CAACI,WAAW,GAAGC;QACnB,CAAC,CAAC,CAAC;QACHrC,UAAU,CAAC,8BAA8B,CAAC;QAC1CiC,UAAU,CAAC,MAAMjC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC+B,QAAQ,CAACR,IAAI,CAACa,OAAO,IAAI,0BAA0B,CAAC;MACjE;IACJ,CAAC,CAAC,OAAOT,GAAG,EAAE;MACV3B,QAAQ,CAAC,6CAA6C,CAAC;IAC3D;EACJ,CAAC;EAED,IAAIH,OAAO,EAAE;IACT,oBACIR,OAAA;MAAKqD,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAClCtD,OAAA;QAAKqD,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC1D,OAAA;QAAAsD,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAEd;EAEA,oBACI1D,OAAA;IAAKqD,SAAS,EAAC,eAAe;IAAAC,QAAA,GACzB5C,KAAK,iBACFV,OAAA;MAAKqD,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAChDtD,OAAA,CAACH,qBAAqB;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzB1D,OAAA;QAAAsD,QAAA,EAAO5C;MAAK;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACR,EAEA9C,OAAO,iBACJZ,OAAA;MAAKqD,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAClDtD,OAAA,CAACJ,OAAO;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACX1D,OAAA;QAAAsD,QAAA,EAAO1C;MAAO;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACR,eAED1D,OAAA;MAAKqD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAE1BtD,OAAA;QAAKqD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BtD,OAAA;UAAKqD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjCtD,OAAA;YAAAsD,QAAA,gBAAItD,OAAA,CAACT,WAAW;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAAkB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1C1D,OAAA;YAAAsD,QAAA,EAAG;UAAwD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eAEN1D,OAAA;UAAKqD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAElCtD,OAAA;YAAKqD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBtD,OAAA;cAAKqD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBtD,OAAA;gBAAKqD,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1BtD,OAAA,CAACR,UAAU;kBAAC6D,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,2BAE3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1D,OAAA;gBAAKqD,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1D,OAAA;gBAAKqD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAAC,UACpB,EAACpC,gBAAgB,CAACE,WAAW,GAAG,WAAW,GAAG,YAAY;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACN1D,OAAA;cAAKqD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5BtD,OAAA;gBACIqD,SAAS,EAAE,cAAcnC,gBAAgB,CAACE,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACxEuC,OAAO,EAAEnB,eAAgB;gBAAAc,QAAA,EAExBpC,gBAAgB,CAACE,WAAW,gBAAGpB,OAAA,CAACN,UAAU;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG1D,OAAA,CAACL,WAAW;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN1D,OAAA;YAAKqD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBtD,OAAA;cAAKqD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBtD,OAAA;gBAAKqD,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC1BtD,OAAA,CAACT,WAAW;kBAAC8D,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mCAE5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1D,OAAA;gBAAKqD,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1D,OAAA;gBAAKqD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAAC,UACpB,EAACpC,gBAAgB,CAACG,WAAW,GAAG,WAAW,GAAG,YAAY;cAAA;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACN1D,OAAA;cAAKqD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5BtD,OAAA;gBACIqD,SAAS,EAAC,mBAAmB;gBAC7BO,QAAQ;gBAAAN,QAAA,EACX;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1D,OAAA;QAAKqD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BtD,OAAA;UAAKqD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjCtD,OAAA;YAAAsD,QAAA,gBAAItD,OAAA,CAACP,MAAM;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAAyB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C1D,OAAA;YAAAsD,QAAA,EAAG;UAA6C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAEN1D,OAAA;UAAKqD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAClCtD,OAAA;YAAKqD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBtD,OAAA;cAAKqD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBtD,OAAA;gBAAKqD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxD1D,OAAA;gBAAKqD,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACN1D,OAAA;cAAKqD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5BtD,OAAA;gBACIqD,SAAS,EAAE,cAAc9B,eAAe,CAACE,mBAAmB,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAC/EkC,OAAO,EAAEA,CAAA,KAAMX,0BAA0B,CAAC,qBAAqB,CAAE;gBAAAM,QAAA,EAEhE/B,eAAe,CAACE,mBAAmB,gBAAGzB,OAAA,CAACN,UAAU;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG1D,OAAA,CAACL,WAAW;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN1D,OAAA;YAAKqD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBtD,OAAA;cAAKqD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBtD,OAAA;gBAAKqD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpD1D,OAAA;gBAAKqD,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACN1D,OAAA;cAAKqD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5BtD,OAAA;gBACIqD,SAAS,EAAE,cAAc9B,eAAe,CAACG,eAAe,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAC3EiC,OAAO,EAAEA,CAAA,KAAMX,0BAA0B,CAAC,iBAAiB,CAAE;gBAAAM,QAAA,EAE5D/B,eAAe,CAACG,eAAe,gBAAG1B,OAAA,CAACN,UAAU;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG1D,OAAA,CAACL,WAAW;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN1D,OAAA;YAAKqD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBtD,OAAA;cAAKqD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBtD,OAAA;gBAAKqD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxD1D,OAAA;gBAAKqD,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACN1D,OAAA;cAAKqD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5BtD,OAAA;gBACIqD,SAAS,EAAE,cAAc9B,eAAe,CAACI,mBAAmB,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAC/EgC,OAAO,EAAEA,CAAA,KAAMX,0BAA0B,CAAC,qBAAqB,CAAE;gBAAAM,QAAA,EAEhE/B,eAAe,CAACI,mBAAmB,gBAAG3B,OAAA,CAACN,UAAU;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG1D,OAAA,CAACL,WAAW;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN1D,OAAA;QAAKqD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BtD,OAAA;UAAKqD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjCtD,OAAA;YAAAsD,QAAA,gBAAItD,OAAA,CAACF,YAAY;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAAkB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3C1D,OAAA;YAAAsD,QAAA,EAAG;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAEN1D,OAAA;UAAKqD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAClCtD,OAAA;YAAKqD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBtD,OAAA;cAAAsD,QAAA,gBAAGtD,OAAA;gBAAAsD,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC5C,MAAM;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzC1D,OAAA;cAAAsD,QAAA,gBAAGtD,OAAA;gBAAAsD,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC1C,SAAS;YAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1C1D,OAAA;cAAAsD,QAAA,gBAAGtD,OAAA;gBAAAsD,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxC,gBAAgB,CAACI,WAAW;YAAA;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnE1D,OAAA;cAAAsD,QAAA,gBAAGtD,OAAA;gBAAAsD,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxC,gBAAgB,CAACE,WAAW,GAAG,KAAK,GAAG,IAAI;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClF1D,OAAA;cAAAsD,QAAA,gBAAGtD,OAAA;gBAAAsD,QAAA,EAAQ;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxC,gBAAgB,CAACG,WAAW,GAAG,KAAK,GAAG,IAAI;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACpD,EAAA,CAnSQD,YAAY;EAAA,QACAhB,WAAW;AAAA;AAAAwE,EAAA,GADvBxD,YAAY;AAqSrB,eAAeA,YAAY;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}