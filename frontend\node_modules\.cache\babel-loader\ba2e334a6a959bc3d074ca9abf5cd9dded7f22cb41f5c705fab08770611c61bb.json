{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\User\\\\User2FASetup.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { FaShieldAlt, FaQrcode, FaKey, FaArrowLeft, FaCheck, FaExclamationTriangle, FaCopy, FaDownload } from 'react-icons/fa';\nimport './User2FASetup.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\nfunction User2FASetup({\n  userId,\n  userEmail,\n  onSuccess,\n  onBack\n}) {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [step, setStep] = useState(1); // 1: Setup, 2: Verify, 3: Complete\n\n  // 2FA Setup data\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\n  const [secretKey, setSecretKey] = useState('');\n  const [backupCodes, setBackupCodes] = useState([]);\n  const [verificationCode, setVerificationCode] = useState('');\n  const start2FASetup = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_setup_2fa.php`, {\n        userId: userId,\n        action: 'generate'\n      });\n      if (response.data.success) {\n        setQrCodeUrl(response.data.qr_code_url);\n        setSecretKey(response.data.secret);\n        setStep(2);\n      } else {\n        setError(response.data.message || 'Failed to generate 2FA setup');\n      }\n    } catch (err) {\n      console.error('2FA setup error:', err);\n      setError('Failed to start 2FA setup. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const verify2FACode = async () => {\n    if (!verificationCode || verificationCode.length !== 6) {\n      setError('Please enter a valid 6-digit code');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_setup_2fa.php`, {\n        userId: userId,\n        action: 'verify',\n        code: verificationCode\n      });\n      if (response.data.success) {\n        setBackupCodes(response.data.backup_codes || []);\n        setStep(3);\n        setSuccess('2FA has been successfully enabled!');\n      } else {\n        setError(response.data.message || 'Invalid verification code');\n      }\n    } catch (err) {\n      console.error('2FA verification error:', err);\n      setError('Failed to verify code. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const complete2FASetup = () => {\n    if (onSuccess) {\n      onSuccess();\n    }\n  };\n  const copyToClipboard = text => {\n    navigator.clipboard.writeText(text).then(() => {\n      setSuccess('Copied to clipboard!');\n      setTimeout(() => setSuccess(''), 2000);\n    });\n  };\n  const downloadBackupCodes = () => {\n    const content = `FanBet247 2FA Backup Codes\\n\\nGenerated: ${new Date().toLocaleString()}\\nUser: ${userEmail}\\n\\nBackup Codes:\\n${backupCodes.join('\\n')}\\n\\nKeep these codes safe! Each code can only be used once.`;\n    const blob = new Blob([content], {\n      type: 'text/plain'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'fanbet247-backup-codes.txt';\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    window.URL.revokeObjectURL(url);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-2fa-setup\",\n    children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"setup-alert setup-alert-error\",\n      children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"setup-alert setup-alert-success\",\n      children: [/*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 17\n    }, this), step === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"setup-step\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setup-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-btn\",\n          onClick: onBack,\n          children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 29\n          }, this), \" Back to Settings\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 29\n          }, this), \" Setup Two-Factor Authentication\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setup-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setup-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Enhance Your Account Security\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Two-factor authentication adds an extra layer of security to your account by requiring a verification code from your mobile device in addition to your password.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setup-requirements\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"What you'll need:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"A smartphone or tablet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"An authenticator app (Google Authenticator, Authy, etc.)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"A few minutes to complete the setup\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setup-benefits\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Benefits:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\uD83D\\uDD12 Enhanced account security\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\uD83D\\uDEE1\\uFE0F Protection against unauthorized access\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\uD83D\\uDCF1 Works offline with your mobile device\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\uD83D\\uDD11 Backup codes for emergency access\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setup-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: start2FASetup,\n            disabled: loading,\n            children: loading ? 'Setting up...' : 'Start Setup'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 17\n    }, this), step === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"setup-step\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setup-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-btn\",\n          onClick: () => setStep(1),\n          children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 29\n          }, this), \" Back\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(FaQrcode, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 29\n          }, this), \" Scan QR Code\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setup-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qr-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qr-instructions\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Step 1: Scan the QR Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Open your authenticator app and scan this QR code:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qr-code-container\",\n            children: qrCodeUrl && /*#__PURE__*/_jsxDEV(\"img\", {\n              src: qrCodeUrl,\n              alt: \"2FA QR Code\",\n              className: \"qr-code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"manual-entry\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Can't scan? Enter manually:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"secret-key\",\n              children: [/*#__PURE__*/_jsxDEV(\"code\", {\n                children: secretKey\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"copy-btn\",\n                onClick: () => copyToClipboard(secretKey),\n                children: /*#__PURE__*/_jsxDEV(FaCopy, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"verification-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Step 2: Enter Verification Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Enter the 6-digit code from your authenticator app:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"code-input-container\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"code-input\",\n              placeholder: \"000000\",\n              value: verificationCode,\n              onChange: e => setVerificationCode(e.target.value.replace(/\\D/g, '').slice(0, 6)),\n              maxLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: verify2FACode,\n            disabled: loading || verificationCode.length !== 6,\n            children: loading ? 'Verifying...' : 'Verify & Enable 2FA'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 17\n    }, this), step === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"setup-step\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setup-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(FaKey, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 29\n          }, this), \" Save Your Backup Codes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setup-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"backup-codes-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"backup-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83C\\uDF89 2FA Successfully Enabled!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Save these backup codes in a safe place. You can use them to access your account if you lose your phone.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"backup-warning\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Important:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 37\n              }, this), \" Each code can only be used once. Store them securely!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"backup-codes\",\n            children: backupCodes.map((code, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"backup-code\",\n              children: /*#__PURE__*/_jsxDEV(\"code\", {\n                children: code\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 41\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"backup-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: downloadBackupCodes,\n              children: [/*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 37\n              }, this), \" Download Codes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => copyToClipboard(backupCodes.join('\\n')),\n              children: [/*#__PURE__*/_jsxDEV(FaCopy, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 37\n              }, this), \" Copy All Codes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"completion-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: complete2FASetup,\n              children: \"Complete Setup\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 9\n  }, this);\n}\n_s(User2FASetup, \"si4P2/BEAYyt5ris8T+VqymkaLk=\");\n_c = User2FASetup;\nexport default User2FASetup;\nvar _c;\n$RefreshReg$(_c, \"User2FASetup\");", "map": {"version": 3, "names": ["React", "useState", "axios", "FaShieldAlt", "FaQrcode", "FaKey", "FaArrowLeft", "FaCheck", "FaExclamationTriangle", "FaCopy", "FaDownload", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "User2FASetup", "userId", "userEmail", "onSuccess", "onBack", "_s", "loading", "setLoading", "error", "setError", "success", "setSuccess", "step", "setStep", "qrCodeUrl", "setQrCodeUrl", "secret<PERSON>ey", "set<PERSON>ec<PERSON><PERSON>ey", "backupCodes", "setBackupCodes", "verificationCode", "setVerificationCode", "start2FASetup", "response", "post", "action", "data", "qr_code_url", "secret", "message", "err", "console", "verify2FACode", "length", "code", "backup_codes", "complete2FASetup", "copyToClipboard", "text", "navigator", "clipboard", "writeText", "then", "setTimeout", "downloadBackupCodes", "content", "Date", "toLocaleString", "join", "blob", "Blob", "type", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "src", "alt", "placeholder", "value", "onChange", "e", "target", "replace", "slice", "max<PERSON><PERSON><PERSON>", "map", "index", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/User/User2FASetup.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport { \n    FaShieldAlt, \n    FaQrcode, \n    FaKey, \n    FaArrowLeft, \n    FaCheck,\n    FaExclamationTriangle,\n    FaCopy,\n    FaDownload\n} from 'react-icons/fa';\nimport './User2FASetup.css';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\n\nfunction User2FASetup({ userId, userEmail, onSuccess, onBack }) {\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [step, setStep] = useState(1); // 1: Setup, 2: Verify, 3: Complete\n    \n    // 2FA Setup data\n    const [qrCodeUrl, setQrCodeUrl] = useState('');\n    const [secretKey, setSecretKey] = useState('');\n    const [backupCodes, setBackupCodes] = useState([]);\n    const [verificationCode, setVerificationCode] = useState('');\n\n    const start2FASetup = async () => {\n        try {\n            setLoading(true);\n            setError('');\n\n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/user_setup_2fa.php`,\n                {\n                    userId: userId,\n                    action: 'generate'\n                }\n            );\n\n            if (response.data.success) {\n                setQrCodeUrl(response.data.qr_code_url);\n                setSecretKey(response.data.secret);\n                setStep(2);\n            } else {\n                setError(response.data.message || 'Failed to generate 2FA setup');\n            }\n        } catch (err) {\n            console.error('2FA setup error:', err);\n            setError('Failed to start 2FA setup. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const verify2FACode = async () => {\n        if (!verificationCode || verificationCode.length !== 6) {\n            setError('Please enter a valid 6-digit code');\n            return;\n        }\n\n        try {\n            setLoading(true);\n            setError('');\n\n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/user_setup_2fa.php`,\n                {\n                    userId: userId,\n                    action: 'verify',\n                    code: verificationCode\n                }\n            );\n\n            if (response.data.success) {\n                setBackupCodes(response.data.backup_codes || []);\n                setStep(3);\n                setSuccess('2FA has been successfully enabled!');\n            } else {\n                setError(response.data.message || 'Invalid verification code');\n            }\n        } catch (err) {\n            console.error('2FA verification error:', err);\n            setError('Failed to verify code. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const complete2FASetup = () => {\n        if (onSuccess) {\n            onSuccess();\n        }\n    };\n\n    const copyToClipboard = (text) => {\n        navigator.clipboard.writeText(text).then(() => {\n            setSuccess('Copied to clipboard!');\n            setTimeout(() => setSuccess(''), 2000);\n        });\n    };\n\n    const downloadBackupCodes = () => {\n        const content = `FanBet247 2FA Backup Codes\\n\\nGenerated: ${new Date().toLocaleString()}\\nUser: ${userEmail}\\n\\nBackup Codes:\\n${backupCodes.join('\\n')}\\n\\nKeep these codes safe! Each code can only be used once.`;\n        \n        const blob = new Blob([content], { type: 'text/plain' });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = 'fanbet247-backup-codes.txt';\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n    };\n\n    return (\n        <div className=\"user-2fa-setup\">\n            {error && (\n                <div className=\"setup-alert setup-alert-error\">\n                    <FaExclamationTriangle />\n                    <span>{error}</span>\n                </div>\n            )}\n\n            {success && (\n                <div className=\"setup-alert setup-alert-success\">\n                    <FaCheck />\n                    <span>{success}</span>\n                </div>\n            )}\n\n            {/* Step 1: Introduction */}\n            {step === 1 && (\n                <div className=\"setup-step\">\n                    <div className=\"setup-header\">\n                        <button className=\"back-btn\" onClick={onBack}>\n                            <FaArrowLeft /> Back to Settings\n                        </button>\n                        <h2><FaShieldAlt /> Setup Two-Factor Authentication</h2>\n                    </div>\n\n                    <div className=\"setup-content\">\n                        <div className=\"setup-info\">\n                            <h3>Enhance Your Account Security</h3>\n                            <p>Two-factor authentication adds an extra layer of security to your account by requiring a verification code from your mobile device in addition to your password.</p>\n                            \n                            <div className=\"setup-requirements\">\n                                <h4>What you'll need:</h4>\n                                <ul>\n                                    <li>A smartphone or tablet</li>\n                                    <li>An authenticator app (Google Authenticator, Authy, etc.)</li>\n                                    <li>A few minutes to complete the setup</li>\n                                </ul>\n                            </div>\n\n                            <div className=\"setup-benefits\">\n                                <h4>Benefits:</h4>\n                                <ul>\n                                    <li>🔒 Enhanced account security</li>\n                                    <li>🛡️ Protection against unauthorized access</li>\n                                    <li>📱 Works offline with your mobile device</li>\n                                    <li>🔑 Backup codes for emergency access</li>\n                                </ul>\n                            </div>\n                        </div>\n\n                        <div className=\"setup-actions\">\n                            <button \n                                className=\"btn btn-primary\"\n                                onClick={start2FASetup}\n                                disabled={loading}\n                            >\n                                {loading ? 'Setting up...' : 'Start Setup'}\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* Step 2: QR Code and Verification */}\n            {step === 2 && (\n                <div className=\"setup-step\">\n                    <div className=\"setup-header\">\n                        <button className=\"back-btn\" onClick={() => setStep(1)}>\n                            <FaArrowLeft /> Back\n                        </button>\n                        <h2><FaQrcode /> Scan QR Code</h2>\n                    </div>\n\n                    <div className=\"setup-content\">\n                        <div className=\"qr-section\">\n                            <div className=\"qr-instructions\">\n                                <h3>Step 1: Scan the QR Code</h3>\n                                <p>Open your authenticator app and scan this QR code:</p>\n                            </div>\n\n                            <div className=\"qr-code-container\">\n                                {qrCodeUrl && (\n                                    <img src={qrCodeUrl} alt=\"2FA QR Code\" className=\"qr-code\" />\n                                )}\n                            </div>\n\n                            <div className=\"manual-entry\">\n                                <h4>Can't scan? Enter manually:</h4>\n                                <div className=\"secret-key\">\n                                    <code>{secretKey}</code>\n                                    <button \n                                        className=\"copy-btn\"\n                                        onClick={() => copyToClipboard(secretKey)}\n                                    >\n                                        <FaCopy />\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div className=\"verification-section\">\n                            <h3>Step 2: Enter Verification Code</h3>\n                            <p>Enter the 6-digit code from your authenticator app:</p>\n                            \n                            <div className=\"code-input-container\">\n                                <input\n                                    type=\"text\"\n                                    className=\"code-input\"\n                                    placeholder=\"000000\"\n                                    value={verificationCode}\n                                    onChange={(e) => setVerificationCode(e.target.value.replace(/\\D/g, '').slice(0, 6))}\n                                    maxLength=\"6\"\n                                />\n                            </div>\n\n                            <button \n                                className=\"btn btn-primary\"\n                                onClick={verify2FACode}\n                                disabled={loading || verificationCode.length !== 6}\n                            >\n                                {loading ? 'Verifying...' : 'Verify & Enable 2FA'}\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* Step 3: Backup Codes */}\n            {step === 3 && (\n                <div className=\"setup-step\">\n                    <div className=\"setup-header\">\n                        <h2><FaKey /> Save Your Backup Codes</h2>\n                    </div>\n\n                    <div className=\"setup-content\">\n                        <div className=\"backup-codes-section\">\n                            <div className=\"backup-info\">\n                                <h3>🎉 2FA Successfully Enabled!</h3>\n                                <p>Save these backup codes in a safe place. You can use them to access your account if you lose your phone.</p>\n                                <p className=\"backup-warning\">\n                                    <strong>Important:</strong> Each code can only be used once. Store them securely!\n                                </p>\n                            </div>\n\n                            <div className=\"backup-codes\">\n                                {backupCodes.map((code, index) => (\n                                    <div key={index} className=\"backup-code\">\n                                        <code>{code}</code>\n                                    </div>\n                                ))}\n                            </div>\n\n                            <div className=\"backup-actions\">\n                                <button \n                                    className=\"btn btn-secondary\"\n                                    onClick={downloadBackupCodes}\n                                >\n                                    <FaDownload /> Download Codes\n                                </button>\n                                <button \n                                    className=\"btn btn-secondary\"\n                                    onClick={() => copyToClipboard(backupCodes.join('\\n'))}\n                                >\n                                    <FaCopy /> Copy All Codes\n                                </button>\n                            </div>\n\n                            <div className=\"completion-actions\">\n                                <button \n                                    className=\"btn btn-primary\"\n                                    onClick={complete2FASetup}\n                                >\n                                    Complete Setup\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n}\n\nexport default User2FASetup;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SACIC,WAAW,EACXC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,OAAO,EACPC,qBAAqB,EACrBC,MAAM,EACNC,UAAU,QACP,gBAAgB;AACvB,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,oCAAoC;AAE/F,SAASC,YAAYA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEC,SAAS;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAC5D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4B,IAAI,EAAEC,OAAO,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;EAErC;EACA,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAMsC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACAf,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMc,QAAQ,GAAG,MAAMtC,KAAK,CAACuC,IAAI,CAC7B,GAAG5B,YAAY,8BAA8B,EAC7C;QACIK,MAAM,EAAEA,MAAM;QACdwB,MAAM,EAAE;MACZ,CACJ,CAAC;MAED,IAAIF,QAAQ,CAACG,IAAI,CAAChB,OAAO,EAAE;QACvBK,YAAY,CAACQ,QAAQ,CAACG,IAAI,CAACC,WAAW,CAAC;QACvCV,YAAY,CAACM,QAAQ,CAACG,IAAI,CAACE,MAAM,CAAC;QAClCf,OAAO,CAAC,CAAC,CAAC;MACd,CAAC,MAAM;QACHJ,QAAQ,CAACc,QAAQ,CAACG,IAAI,CAACG,OAAO,IAAI,8BAA8B,CAAC;MACrE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAACvB,KAAK,CAAC,kBAAkB,EAAEsB,GAAG,CAAC;MACtCrB,QAAQ,CAAC,8CAA8C,CAAC;IAC5D,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMyB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACZ,gBAAgB,IAAIA,gBAAgB,CAACa,MAAM,KAAK,CAAC,EAAE;MACpDxB,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACJ;IAEA,IAAI;MACAF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMc,QAAQ,GAAG,MAAMtC,KAAK,CAACuC,IAAI,CAC7B,GAAG5B,YAAY,8BAA8B,EAC7C;QACIK,MAAM,EAAEA,MAAM;QACdwB,MAAM,EAAE,QAAQ;QAChBS,IAAI,EAAEd;MACV,CACJ,CAAC;MAED,IAAIG,QAAQ,CAACG,IAAI,CAAChB,OAAO,EAAE;QACvBS,cAAc,CAACI,QAAQ,CAACG,IAAI,CAACS,YAAY,IAAI,EAAE,CAAC;QAChDtB,OAAO,CAAC,CAAC,CAAC;QACVF,UAAU,CAAC,oCAAoC,CAAC;MACpD,CAAC,MAAM;QACHF,QAAQ,CAACc,QAAQ,CAACG,IAAI,CAACG,OAAO,IAAI,2BAA2B,CAAC;MAClE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAACvB,KAAK,CAAC,yBAAyB,EAAEsB,GAAG,CAAC;MAC7CrB,QAAQ,CAAC,0CAA0C,CAAC;IACxD,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM6B,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAIjC,SAAS,EAAE;MACXA,SAAS,CAAC,CAAC;IACf;EACJ,CAAC;EAED,MAAMkC,eAAe,GAAIC,IAAI,IAAK;IAC9BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,MAAM;MAC3C/B,UAAU,CAAC,sBAAsB,CAAC;MAClCgC,UAAU,CAAC,MAAMhC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC;EACN,CAAC;EAED,MAAMiC,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,OAAO,GAAG,4CAA4C,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,WAAW7C,SAAS,sBAAsBgB,WAAW,CAAC8B,IAAI,CAAC,IAAI,CAAC,6DAA6D;IAEpN,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACL,OAAO,CAAC,EAAE;MAAEM,IAAI,EAAE;IAAa,CAAC,CAAC;IACxD,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IAC5C,MAAMO,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGP,GAAG;IACZI,CAAC,CAACI,QAAQ,GAAG,4BAA4B;IACzCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;IAC5BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;EACnC,CAAC;EAED,oBACIzD,OAAA;IAAKuE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,GAC1B3D,KAAK,iBACFb,OAAA;MAAKuE,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC1CxE,OAAA,CAACJ,qBAAqB;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzB5E,OAAA;QAAAwE,QAAA,EAAO3D;MAAK;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACR,EAEA7D,OAAO,iBACJf,OAAA;MAAKuE,SAAS,EAAC,iCAAiC;MAAAC,QAAA,gBAC5CxE,OAAA,CAACL,OAAO;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACX5E,OAAA;QAAAwE,QAAA,EAAOzD;MAAO;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACR,EAGA3D,IAAI,KAAK,CAAC,iBACPjB,OAAA;MAAKuE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACvBxE,OAAA;QAAKuE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBxE,OAAA;UAAQuE,SAAS,EAAC,UAAU;UAACM,OAAO,EAAEpE,MAAO;UAAA+D,QAAA,gBACzCxE,OAAA,CAACN,WAAW;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBACnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5E,OAAA;UAAAwE,QAAA,gBAAIxE,OAAA,CAACT,WAAW;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oCAAgC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAEN5E,OAAA;QAAKuE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BxE,OAAA;UAAKuE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBxE,OAAA;YAAAwE,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtC5E,OAAA;YAAAwE,QAAA,EAAG;UAAgK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEvK5E,OAAA;YAAKuE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAC/BxE,OAAA;cAAAwE,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1B5E,OAAA;cAAAwE,QAAA,gBACIxE,OAAA;gBAAAwE,QAAA,EAAI;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/B5E,OAAA;gBAAAwE,QAAA,EAAI;cAAwD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjE5E,OAAA;gBAAAwE,QAAA,EAAI;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN5E,OAAA;YAAKuE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BxE,OAAA;cAAAwE,QAAA,EAAI;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB5E,OAAA;cAAAwE,QAAA,gBACIxE,OAAA;gBAAAwE,QAAA,EAAI;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrC5E,OAAA;gBAAAwE,QAAA,EAAI;cAA0C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnD5E,OAAA;gBAAAwE,QAAA,EAAI;cAAwC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjD5E,OAAA;gBAAAwE,QAAA,EAAI;cAAoC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN5E,OAAA;UAAKuE,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC1BxE,OAAA;YACIuE,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAElD,aAAc;YACvBmD,QAAQ,EAAEnE,OAAQ;YAAA6D,QAAA,EAEjB7D,OAAO,GAAG,eAAe,GAAG;UAAa;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAGA3D,IAAI,KAAK,CAAC,iBACPjB,OAAA;MAAKuE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACvBxE,OAAA;QAAKuE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBxE,OAAA;UAAQuE,SAAS,EAAC,UAAU;UAACM,OAAO,EAAEA,CAAA,KAAM3D,OAAO,CAAC,CAAC,CAAE;UAAAsD,QAAA,gBACnDxE,OAAA,CAACN,WAAW;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SACnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5E,OAAA;UAAAwE,QAAA,gBAAIxE,OAAA,CAACR,QAAQ;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAAa;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAEN5E,OAAA;QAAKuE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BxE,OAAA;UAAKuE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBxE,OAAA;YAAKuE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BxE,OAAA;cAAAwE,QAAA,EAAI;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjC5E,OAAA;cAAAwE,QAAA,EAAG;YAAkD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eAEN5E,OAAA;YAAKuE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC7BrD,SAAS,iBACNnB,OAAA;cAAK+E,GAAG,EAAE5D,SAAU;cAAC6D,GAAG,EAAC,aAAa;cAACT,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC/D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEN5E,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBxE,OAAA;cAAAwE,QAAA,EAAI;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpC5E,OAAA;cAAKuE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBxE,OAAA;gBAAAwE,QAAA,EAAOnD;cAAS;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxB5E,OAAA;gBACIuE,SAAS,EAAC,UAAU;gBACpBM,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACrB,SAAS,CAAE;gBAAAmD,QAAA,eAE1CxE,OAAA,CAACH,MAAM;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN5E,OAAA;UAAKuE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjCxE,OAAA;YAAAwE,QAAA,EAAI;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC5E,OAAA;YAAAwE,QAAA,EAAG;UAAmD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAE1D5E,OAAA;YAAKuE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACjCxE,OAAA;cACIwD,IAAI,EAAC,MAAM;cACXe,SAAS,EAAC,YAAY;cACtBU,WAAW,EAAC,QAAQ;cACpBC,KAAK,EAAEzD,gBAAiB;cACxB0D,QAAQ,EAAGC,CAAC,IAAK1D,mBAAmB,CAAC0D,CAAC,CAACC,MAAM,CAACH,KAAK,CAACI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE;cACpFC,SAAS,EAAC;YAAG;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN5E,OAAA;YACIuE,SAAS,EAAC,iBAAiB;YAC3BM,OAAO,EAAExC,aAAc;YACvByC,QAAQ,EAAEnE,OAAO,IAAIc,gBAAgB,CAACa,MAAM,KAAK,CAAE;YAAAkC,QAAA,EAElD7D,OAAO,GAAG,cAAc,GAAG;UAAqB;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAGA3D,IAAI,KAAK,CAAC,iBACPjB,OAAA;MAAKuE,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACvBxE,OAAA;QAAKuE,SAAS,EAAC,cAAc;QAAAC,QAAA,eACzBxE,OAAA;UAAAwE,QAAA,gBAAIxE,OAAA,CAACP,KAAK;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAAuB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAEN5E,OAAA;QAAKuE,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1BxE,OAAA;UAAKuE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjCxE,OAAA;YAAKuE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBxE,OAAA;cAAAwE,QAAA,EAAI;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrC5E,OAAA;cAAAwE,QAAA,EAAG;YAAwG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/G5E,OAAA;cAAGuE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBACzBxE,OAAA;gBAAAwE,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,0DAC/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5E,OAAA;YAAKuE,SAAS,EAAC,cAAc;YAAAC,QAAA,EACxBjD,WAAW,CAACkE,GAAG,CAAC,CAAClD,IAAI,EAAEmD,KAAK,kBACzB1F,OAAA;cAAiBuE,SAAS,EAAC,aAAa;cAAAC,QAAA,eACpCxE,OAAA;gBAAAwE,QAAA,EAAOjC;cAAI;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC,GADbc,KAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN5E,OAAA;YAAKuE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BxE,OAAA;cACIuE,SAAS,EAAC,mBAAmB;cAC7BM,OAAO,EAAE5B,mBAAoB;cAAAuB,QAAA,gBAE7BxE,OAAA,CAACF,UAAU;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAClB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT5E,OAAA;cACIuE,SAAS,EAAC,mBAAmB;cAC7BM,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACnB,WAAW,CAAC8B,IAAI,CAAC,IAAI,CAAC,CAAE;cAAAmB,QAAA,gBAEvDxE,OAAA,CAACH,MAAM;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBACd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEN5E,OAAA;YAAKuE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAC/BxE,OAAA;cACIuE,SAAS,EAAC,iBAAiB;cAC3BM,OAAO,EAAEpC,gBAAiB;cAAA+B,QAAA,EAC7B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAClE,EAAA,CA3RQL,YAAY;AAAAsF,EAAA,GAAZtF,YAAY;AA6RrB,eAAeA,YAAY;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}