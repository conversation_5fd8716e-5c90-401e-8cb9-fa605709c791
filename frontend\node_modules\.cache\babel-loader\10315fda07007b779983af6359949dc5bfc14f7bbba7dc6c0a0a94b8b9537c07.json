{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { Link, useNavigate } from 'react-router-dom';\nimport Countdown from 'react-countdown';\nimport { useUser } from '../context/UserContext';\nimport { FaArrowRight, FaChevronRight } from 'react-icons/fa';\n// import { CurrencyBalance, CurrencyBetAmount } from '../components/Currency'; // DISABLED: Currency system being recoded\nimport { API_BASE_URL } from '../config';\nimport './UserDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction UserDashboard() {\n  _s();\n  const navigate = useNavigate();\n  const [user, setUser] = useState({});\n  const {\n    setUserData\n  } = useUser();\n  const [error, setError] = useState(null);\n  const [greeting, setGreeting] = useState('');\n  const [recentBets, setRecentBets] = useState([]);\n  const [recentChallenges, setRecentChallenges] = useState([]);\n  const [teams, setTeams] = useState([]);\n  const fetchUserData = async () => {\n    try {\n      console.log('Fetching user data...');\n      const userId = localStorage.getItem('userId');\n      const response = await axios.get(`user_data.php?id=${userId}`);\n      console.log('User data response:', response.data);\n      if (response.data.success) {\n        setUser(response.data.user);\n        setUserData({\n          balance: response.data.user.balance || 0,\n          points: response.data.user.points || 0,\n          username: response.data.user.username || ''\n        });\n      } else {\n        console.error('Failed to fetch user data:', response.data.message);\n        throw new Error(response.data.message || 'Failed to fetch user data');\n      }\n    } catch (error) {\n      console.error('Error fetching user data:', error);\n      throw error;\n    }\n  };\n  useEffect(() => {\n    const userId = localStorage.getItem('userId');\n    if (!userId) {\n      navigate('/user/login');\n      return;\n    }\n    const initializeDashboard = async () => {\n      try {\n        setError(null);\n        const results = await Promise.allSettled([fetchUserData(), fetchUserBets(), fetchRecentChallenges(), fetchTeams()]);\n        results.forEach((result, index) => {\n          if (result.status === 'rejected') {\n            console.error(`Error in Promise ${index}:`, result.reason);\n          }\n        });\n        setGreeting(getGreeting());\n      } catch (err) {\n        console.error('Dashboard initialization error:', err);\n        setError('Failed to load dashboard data. Please try again later.');\n      }\n    };\n    initializeDashboard();\n  }, [navigate]); // Removed fetchUserData to prevent infinite loop\n\n  const fetchUserBets = async () => {\n    try {\n      console.log('Fetching user bets...');\n      const userId = localStorage.getItem('userId');\n      const response = await axios.get(`get_user_bets.php?userId=${userId}`);\n      console.log('User bets response:', response.data);\n      if (response.data.success) {\n        setRecentBets(response.data.bets || []);\n      } else {\n        console.error('Failed to fetch user bets:', response.data.message);\n        throw new Error(response.data.message || 'Failed to fetch user bets');\n      }\n    } catch (error) {\n      console.error('Error fetching user bets:', error);\n      throw error;\n    }\n  };\n  const fetchRecentChallenges = async () => {\n    try {\n      console.log('Fetching recent challenges...');\n      const response = await axios.get(`${API_BASE_URL}/handlers/recent_challenges.php`);\n      console.log('Recent challenges response:', response.data);\n      if (response.data.success) {\n        const challengesWithDate = (response.data.challenges || []).map(challenge => ({\n          ...challenge,\n          end_time: new Date(challenge.end_time)\n        }));\n        setRecentChallenges(challengesWithDate);\n      } else {\n        console.error('Failed to fetch recent challenges:', response.data.message);\n        throw new Error(response.data.message || 'Failed to fetch recent challenges');\n      }\n    } catch (error) {\n      console.error('Error fetching recent challenges:', error);\n      throw error;\n    }\n  };\n  const fetchTeams = async () => {\n    try {\n      console.log('Fetching teams...');\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      console.log('Teams response:', response.data);\n      if (response.data.status === 200) {\n        setTeams(response.data.data || []);\n      } else {\n        console.error('Failed to fetch teams:', response.data.message);\n        throw new Error(response.data.message || 'Failed to fetch teams');\n      }\n    } catch (err) {\n      console.error('Error fetching teams:', err);\n      throw err;\n    }\n  };\n  const getTeamLogo = teamName => {\n    const team = teams.find(team => team.name === teamName);\n    return team ? `${API_BASE_URL}/${team.logo}` : '';\n  };\n  const getGreeting = () => {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Good morning';\n    if (hour < 18) return 'Good afternoon';\n    return 'Good evening';\n  };\n  const CountdownRenderer = ({\n    days,\n    hours,\n    minutes,\n    seconds,\n    completed\n  }) => {\n    if (completed) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"status-badge expired\",\n        children: \"Expired\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 20\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      children: [days, \"d:\", hours, \"h:\", minutes, \"m:\", seconds, \"s\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 13\n    }, this);\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-dashboard\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: [greeting, \", \", user.username || 'User', \"!\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-grid\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"account-balance\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Account Balance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"balance-display\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"balance-amount\",\n            children: [\"$\", parseFloat(user.balance || 0).toFixed(2), \" FC\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/deposit\",\n            className: \"primary-button\",\n            children: \"Deposit FanCoins\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/withdraw\",\n            className: \"secondary-button\",\n            children: \"Withdraw FanCoins\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"recent-bets-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Recent Bets\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/user/bets\",\n          className: \"view-all-button\",\n          children: [\"View All \", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n            className: \"view-all-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 34\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bets-list\",\n        children: recentBets.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-data\",\n          children: \"No recent bets found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 25\n        }, this) : recentBets.map(bet => {\n          var _bet$unique_code;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bet-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bet-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: `bet-status ${bet.bet_status}`,\n                children: bet.bet_status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bet-reference\",\n                children: [\"REF: \", ((_bet$unique_code = bet.unique_code) === null || _bet$unique_code === void 0 ? void 0 : _bet$unique_code.toUpperCase()) || `${bet.bet_id}DNRBKCC`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"potential-returns\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"return-item win\",\n                children: [\"Win: $\", parseFloat(bet.potential_return_win_user1 || 0).toFixed(2), \" FC\"]\n              }, \"win\", true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"return-item draw\",\n                children: [\"Draw: $\", parseFloat(bet.potential_return_draw_user1 || 0).toFixed(2), \" FC\"]\n              }, \"draw\", true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"return-item loss\",\n                children: [\"Loss: $\", parseFloat(bet.potential_return_loss_user1 || 0).toFixed(2), \" FC\"]\n              }, \"loss\", true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bet-matchup\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bet-team\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getTeamLogo(bet.team_a),\n                  alt: bet.team_a,\n                  className: \"team-logo-medium\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"team-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"team-name\",\n                    children: bet.team_a\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"username\",\n                    children: bet.bet_choice_user1 === 'team_a_win' ? bet.user1_name : bet.user2_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bet-amount\",\n                    children: [\"$\", parseFloat((bet.bet_choice_user1 === 'team_a_win' ? bet.amount_user1 : bet.amount_user2) || 0).toFixed(2), \" FC\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"vs-badge\",\n                children: \"VS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bet-team\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: getTeamLogo(bet.team_b),\n                  alt: bet.team_b,\n                  className: \"team-logo-medium\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"team-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"team-name\",\n                    children: bet.team_b\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"username\",\n                    children: bet.bet_choice_user1 === 'team_b_win' ? bet.user1_name : bet.user2_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"bet-amount\",\n                    children: [\"$\", parseFloat((bet.bet_choice_user1 === 'team_b_win' ? bet.amount_user1 : bet.amount_user2) || 0).toFixed(2), \" FC\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 33\n            }, this)]\n          }, bet.bet_id || `bet-${bet.unique_code}`, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 29\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-recent-challenges\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Recent Challenges\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/user/challenges\",\n          className: \"dashboard-view-all-button\",\n          children: [\"View All \", /*#__PURE__*/_jsxDEV(FaChevronRight, {\n            className: \"view-all-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 34\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-challenges-list\",\n        children: recentChallenges.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-no-data\",\n          children: \"No recent challenges found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-challenges-grid\",\n          children: recentChallenges.slice(0, 3).map(challenge => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dashboard-challenge-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dashboard-challenge-teams\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"dashboard-challenge-team\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dashboard-team-logo-wrapper\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(challenge.team_a),\n                    alt: challenge.team_a,\n                    className: \"dashboard-team-logo\",\n                    onError: e => {\n                      e.target.src = '/default-team-logo.png';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"dashboard-team-name\",\n                  children: challenge.team_a\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 45\n                }, this)]\n              }, \"team-a\", true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"dashboard-vs-badge\",\n                children: \"VS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"dashboard-challenge-team\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dashboard-team-logo-wrapper\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: getTeamLogo(challenge.team_b),\n                    alt: challenge.team_b,\n                    className: \"dashboard-team-logo\",\n                    onError: e => {\n                      e.target.src = '/default-team-logo.png';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"dashboard-team-name\",\n                  children: challenge.team_b\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 45\n                }, this)]\n              }, \"team-b\", true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dashboard-challenge-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"dashboard-challenge-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"dashboard-challenge-stats\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dashboard-stat-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"dashboard-stat-label\",\n                      children: \"Status:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `dashboard-challenge-status dashboard-status-${challenge.status.toLowerCase()}`,\n                      children: challenge.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dashboard-stat-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"dashboard-stat-label\",\n                      children: \"Goal Advantage:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 312,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"dashboard-stat-value\",\n                      children: [\"+\", challenge.team_a_goal_advantage, \" / +\", challenge.team_b_goal_advantage]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 313,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"dashboard-stat-row\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"dashboard-stat-label\",\n                      children: \"Odds:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"dashboard-stat-value\",\n                      children: [challenge.odds_team_a, \" - \", challenge.odds_team_b]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"dashboard-challenge-date\",\n                  children: new Date(challenge.match_date).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"dashboard-challenge-timer\",\n                children: /*#__PURE__*/_jsxDEV(Countdown, {\n                  date: challenge.end_time,\n                  renderer: CountdownRenderer\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: `/user/join-challenge/${challenge.challenge_id}`,\n                className: \"dashboard-place-bet-button\",\n                children: \"Place Bet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 37\n            }, this)]\n          }, challenge.challenge_id || `challenge-${challenge.id}`, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 9\n  }, this);\n}\n_s(UserDashboard, \"lnnJceUZ3YEp+NxpVKc9xlMuIHA=\", false, function () {\n  return [useNavigate, useUser];\n});\n_c = UserDashboard;\nexport default UserDashboard;\nvar _c;\n$RefreshReg$(_c, \"UserDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Link", "useNavigate", "Countdown", "useUser", "FaArrowRight", "FaChevronRight", "API_BASE_URL", "jsxDEV", "_jsxDEV", "UserDashboard", "_s", "navigate", "user", "setUser", "setUserData", "error", "setError", "greeting", "setGreeting", "recentBets", "setRecentBets", "recentChallenges", "setRecentChallenges", "teams", "setTeams", "fetchUserData", "console", "log", "userId", "localStorage", "getItem", "response", "get", "data", "success", "balance", "points", "username", "message", "Error", "initializeDashboard", "results", "Promise", "allSettled", "fetchUserBets", "fetchRecentChallenges", "fetchTeams", "for<PERSON>ach", "result", "index", "status", "reason", "getGreeting", "err", "bets", "challengesWithDate", "challenges", "map", "challenge", "end_time", "Date", "getTeamLogo", "teamName", "team", "find", "name", "logo", "hour", "getHours", "CountdownRenderer", "days", "hours", "minutes", "seconds", "completed", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "parseFloat", "toFixed", "to", "length", "bet", "_bet$unique_code", "bet_status", "unique_code", "toUpperCase", "bet_id", "potential_return_win_user1", "potential_return_draw_user1", "potential_return_loss_user1", "src", "team_a", "alt", "bet_choice_user1", "user1_name", "user2_name", "amount_user1", "amount_user2", "team_b", "slice", "onError", "e", "target", "toLowerCase", "team_a_goal_advantage", "team_b_goal_advantage", "odds_team_a", "odds_team_b", "match_date", "toLocaleDateString", "date", "renderer", "challenge_id", "id", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { Link, useNavigate } from 'react-router-dom';\nimport Countdown from 'react-countdown';\nimport { useUser } from '../context/UserContext';\nimport { FaArrowRight, FaChevronRight } from 'react-icons/fa';\n// import { CurrencyBalance, CurrencyBetAmount } from '../components/Currency'; // DISABLED: Currency system being recoded\nimport { API_BASE_URL } from '../config';\nimport './UserDashboard.css';\n\nfunction UserDashboard() {\n    const navigate = useNavigate();\n    const [user, setUser] = useState({});\n    const { setUserData } = useUser();\n    const [error, setError] = useState(null);\n    const [greeting, setGreeting] = useState('');\n    const [recentBets, setRecentBets] = useState([]);\n    const [recentChallenges, setRecentChallenges] = useState([]);\n    const [teams, setTeams] = useState([]);\n\n    const fetchUserData = async () => {\n        try {\n            console.log('Fetching user data...');\n            const userId = localStorage.getItem('userId');\n            const response = await axios.get(`user_data.php?id=${userId}`);\n            console.log('User data response:', response.data);\n            \n            if (response.data.success) {\n                setUser(response.data.user);\n                setUserData({\n                    balance: response.data.user.balance || 0,\n                    points: response.data.user.points || 0,\n                    username: response.data.user.username || ''\n                });\n            } else {\n                console.error('Failed to fetch user data:', response.data.message);\n                throw new Error(response.data.message || 'Failed to fetch user data');\n            }\n        } catch (error) {\n            console.error('Error fetching user data:', error);\n            throw error;\n        }\n    };\n\n    useEffect(() => {\n        const userId = localStorage.getItem('userId');\n        if (!userId) {\n            navigate('/user/login');\n            return;\n        }\n\n        const initializeDashboard = async () => {\n            try {\n                setError(null);\n                \n                const results = await Promise.allSettled([\n                    fetchUserData(),\n                    fetchUserBets(),\n                    fetchRecentChallenges(),\n                    fetchTeams()\n                ]);\n\n                results.forEach((result, index) => {\n                    if (result.status === 'rejected') {\n                        console.error(`Error in Promise ${index}:`, result.reason);\n                    }\n                });\n\n                setGreeting(getGreeting());\n            } catch (err) {\n                console.error('Dashboard initialization error:', err);\n                setError('Failed to load dashboard data. Please try again later.');\n            }\n        };\n\n        initializeDashboard();\n    }, [navigate]); // Removed fetchUserData to prevent infinite loop\n\n    const fetchUserBets = async () => {\n        try {\n            console.log('Fetching user bets...');\n            const userId = localStorage.getItem('userId');\n            const response = await axios.get(`get_user_bets.php?userId=${userId}`);\n            console.log('User bets response:', response.data);\n            \n            if (response.data.success) {\n                setRecentBets(response.data.bets || []);\n            } else {\n                console.error('Failed to fetch user bets:', response.data.message);\n                throw new Error(response.data.message || 'Failed to fetch user bets');\n            }\n        } catch (error) {\n            console.error('Error fetching user bets:', error);\n            throw error;\n        }\n    };\n\n    const fetchRecentChallenges = async () => {\n        try {\n            console.log('Fetching recent challenges...');\n            const response = await axios.get(`${API_BASE_URL}/handlers/recent_challenges.php`);\n            console.log('Recent challenges response:', response.data);\n            \n            if (response.data.success) {\n                const challengesWithDate = (response.data.challenges || []).map(challenge => ({\n                    ...challenge,\n                    end_time: new Date(challenge.end_time)\n                }));\n                setRecentChallenges(challengesWithDate);\n            } else {\n                console.error('Failed to fetch recent challenges:', response.data.message);\n                throw new Error(response.data.message || 'Failed to fetch recent challenges');\n            }\n        } catch (error) {\n            console.error('Error fetching recent challenges:', error);\n            throw error;\n        }\n    };\n\n    const fetchTeams = async () => {\n        try {\n            console.log('Fetching teams...');\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n            console.log('Teams response:', response.data);\n            \n            if (response.data.status === 200) {\n                setTeams(response.data.data || []);\n            } else {\n                console.error('Failed to fetch teams:', response.data.message);\n                throw new Error(response.data.message || 'Failed to fetch teams');\n            }\n        } catch (err) {\n            console.error('Error fetching teams:', err);\n            throw err;\n        }\n    };\n\n    const getTeamLogo = (teamName) => {\n        const team = teams.find(team => team.name === teamName);\n        return team ? `${API_BASE_URL}/${team.logo}` : '';\n    };\n\n    const getGreeting = () => {\n        const hour = new Date().getHours();\n        if (hour < 12) return 'Good morning';\n        if (hour < 18) return 'Good afternoon';\n        return 'Good evening';\n    };\n\n    const CountdownRenderer = ({ days, hours, minutes, seconds, completed }) => {\n        if (completed) {\n            return <span className=\"status-badge expired\">Expired</span>;\n        }\n        return (\n            <span>\n                {days}d:{hours}h:{minutes}m:{seconds}s\n            </span>\n        );\n    };\n\n    if (error) {\n        return (\n            <div className=\"user-dashboard\">\n                <div className=\"error-message\">{error}</div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"user-dashboard\">\n            <h1>{greeting}, {user.username || 'User'}!</h1>\n            <div className=\"dashboard-grid\">\n                <div className=\"account-balance\">\n                    <h2>Account Balance</h2>\n                    <div className=\"balance-display\">\n                        {/* <CurrencyBalance\n                            amount={user.balance || 0}\n                            size=\"large\"\n                            className=\"dashboard-balance\"\n                        /> */}\n                        <span className=\"balance-amount\">${parseFloat(user.balance || 0).toFixed(2)} FC</span>\n                    </div>\n                    <div className=\"action-buttons\">\n                        <Link to=\"/deposit\" className=\"primary-button\">Deposit FanCoins</Link>\n                        <Link to=\"/withdraw\" className=\"secondary-button\">Withdraw FanCoins</Link>\n                    </div>\n                </div>\n            </div>\n\n            <div className=\"recent-bets-container\">\n                <div className=\"section-header\">\n                    <h2>Recent Bets</h2>\n                    <Link to=\"/user/bets\" className=\"view-all-button\">\n                        View All <FaArrowRight className=\"view-all-icon\" />\n                    </Link>\n                </div>\n                <div className=\"bets-list\">\n                    {recentBets.length === 0 ? (\n                        <div className=\"no-data\">No recent bets found</div>\n                    ) : (\n                        recentBets.map(bet => (\n                            <div key={bet.bet_id || `bet-${bet.unique_code}`} className=\"bet-card\">\n                                <div className=\"bet-header\">\n                                    <span className={`bet-status ${bet.bet_status}`}>\n                                        {bet.bet_status}\n                                    </span>\n                                    <span className=\"bet-reference\">\n                                        REF: {bet.unique_code?.toUpperCase() || `${bet.bet_id}DNRBKCC`}\n                                    </span>\n                                </div>\n                                \n                                <div className=\"potential-returns\">\n                                    <div key=\"win\" className=\"return-item win\">\n                                        Win: ${parseFloat(bet.potential_return_win_user1 || 0).toFixed(2)} FC\n                                    </div>\n                                    <div key=\"draw\" className=\"return-item draw\">\n                                        Draw: ${parseFloat(bet.potential_return_draw_user1 || 0).toFixed(2)} FC\n                                    </div>\n                                    <div key=\"loss\" className=\"return-item loss\">\n                                        Loss: ${parseFloat(bet.potential_return_loss_user1 || 0).toFixed(2)} FC\n                                    </div>\n                                </div>\n\n                                <div className=\"bet-matchup\">\n                                    <div className=\"bet-team\">\n                                        <img src={getTeamLogo(bet.team_a)} alt={bet.team_a} className=\"team-logo-medium\" />\n                                        <div className=\"team-details\">\n                                            <span className=\"team-name\">{bet.team_a}</span>\n                                            <span className=\"username\">\n                                                {bet.bet_choice_user1 === 'team_a_win' ? bet.user1_name : bet.user2_name}\n                                            </span>\n                                            <span className=\"bet-amount\">\n                                                ${parseFloat((bet.bet_choice_user1 === 'team_a_win' ? bet.amount_user1 : bet.amount_user2) || 0).toFixed(2)} FC\n                                            </span>\n                                        </div>\n                                    </div>\n\n                                    <div className=\"vs-badge\">VS</div>\n\n                                    <div className=\"bet-team\">\n                                        <img src={getTeamLogo(bet.team_b)} alt={bet.team_b} className=\"team-logo-medium\" />\n                                        <div className=\"team-details\">\n                                            <span className=\"team-name\">{bet.team_b}</span>\n                                            <span className=\"username\">\n                                                {bet.bet_choice_user1 === 'team_b_win' ? bet.user1_name : bet.user2_name}\n                                            </span>\n                                            <span className=\"bet-amount\">\n                                                ${parseFloat((bet.bet_choice_user1 === 'team_b_win' ? bet.amount_user1 : bet.amount_user2) || 0).toFixed(2)} FC\n                                            </span>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        ))\n                    )}\n                </div>\n            </div>\n\n            <div className=\"dashboard-recent-challenges\">\n                <div className=\"dashboard-section-header\">\n                    <h2>Recent Challenges</h2>\n                    <Link to=\"/user/challenges\" className=\"dashboard-view-all-button\">\n                        View All <FaChevronRight className=\"view-all-icon\" />\n                    </Link>\n                </div>\n                <div className=\"dashboard-challenges-list\">\n                    {recentChallenges.length === 0 ? (\n                        <div className=\"dashboard-no-data\">No recent challenges found</div>\n                    ) : (\n                        <div className=\"dashboard-challenges-grid\">\n                            {recentChallenges.slice(0, 3).map((challenge) => (\n                                <div key={challenge.challenge_id || `challenge-${challenge.id}`} className=\"dashboard-challenge-card\">\n                                    <div className=\"dashboard-challenge-teams\">\n                                        <div key=\"team-a\" className=\"dashboard-challenge-team\">\n                                            <div className=\"dashboard-team-logo-wrapper\">\n                                                <img\n                                                    src={getTeamLogo(challenge.team_a)}\n                                                    alt={challenge.team_a}\n                                                    className=\"dashboard-team-logo\"\n                                                    onError={(e) => {\n                                                        e.target.src = '/default-team-logo.png';\n                                                    }}\n                                                />\n                                            </div>\n                                            <span className=\"dashboard-team-name\">{challenge.team_a}</span>\n                                        </div>\n                                        <div className=\"dashboard-vs-badge\">VS</div>\n                                        <div key=\"team-b\" className=\"dashboard-challenge-team\">\n                                            <div className=\"dashboard-team-logo-wrapper\">\n                                                <img\n                                                    src={getTeamLogo(challenge.team_b)}\n                                                    alt={challenge.team_b}\n                                                    className=\"dashboard-team-logo\"\n                                                    onError={(e) => {\n                                                        e.target.src = '/default-team-logo.png';\n                                                    }}\n                                                />\n                                            </div>\n                                            <span className=\"dashboard-team-name\">{challenge.team_b}</span>\n                                        </div>\n                                    </div>\n                                    <div className=\"dashboard-challenge-details\">\n                                        <div className=\"dashboard-challenge-info\">\n                                            <div className=\"dashboard-challenge-stats\">\n                                                <div className=\"dashboard-stat-row\">\n                                                    <span className=\"dashboard-stat-label\">Status:</span>\n                                                    <span className={`dashboard-challenge-status dashboard-status-${challenge.status.toLowerCase()}`}>\n                                                        {challenge.status}\n                                                    </span>\n                                                </div>\n                                                <div className=\"dashboard-stat-row\">\n                                                    <span className=\"dashboard-stat-label\">Goal Advantage:</span>\n                                                    <span className=\"dashboard-stat-value\">+{challenge.team_a_goal_advantage} / +{challenge.team_b_goal_advantage}</span>\n                                                </div>\n                                                <div className=\"dashboard-stat-row\">\n                                                    <span className=\"dashboard-stat-label\">Odds:</span>\n                                                    <span className=\"dashboard-stat-value\">{challenge.odds_team_a} - {challenge.odds_team_b}</span>\n                                                </div>\n                                            </div>\n                                            <span className=\"dashboard-challenge-date\">\n                                                {new Date(challenge.match_date).toLocaleDateString()}\n                                            </span>\n                                        </div>\n                                        <div className=\"dashboard-challenge-timer\">\n                                            <Countdown\n                                                date={challenge.end_time}\n                                                renderer={CountdownRenderer}\n                                            />\n                                        </div>\n                                        <Link \n                                            to={`/user/join-challenge/${challenge.challenge_id}`} \n                                            className=\"dashboard-place-bet-button\"\n                                        >\n                                            Place Bet\n                                        </Link>\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                    )}\n                </div>\n            </div>\n        </div>\n    );\n}\n\nexport default UserDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,SAAS,MAAM,iBAAiB;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;AAC7D;AACA,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpC,MAAM;IAAEiB;EAAY,CAAC,GAAGX,OAAO,CAAC,CAAC;EACjC,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM4B,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACAC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpC,MAAMC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MAC7C,MAAMC,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,oBAAoBJ,MAAM,EAAE,CAAC;MAC9DF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEI,QAAQ,CAACE,IAAI,CAAC;MAEjD,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBrB,OAAO,CAACkB,QAAQ,CAACE,IAAI,CAACrB,IAAI,CAAC;QAC3BE,WAAW,CAAC;UACRqB,OAAO,EAAEJ,QAAQ,CAACE,IAAI,CAACrB,IAAI,CAACuB,OAAO,IAAI,CAAC;UACxCC,MAAM,EAAEL,QAAQ,CAACE,IAAI,CAACrB,IAAI,CAACwB,MAAM,IAAI,CAAC;UACtCC,QAAQ,EAAEN,QAAQ,CAACE,IAAI,CAACrB,IAAI,CAACyB,QAAQ,IAAI;QAC7C,CAAC,CAAC;MACN,CAAC,MAAM;QACHX,OAAO,CAACX,KAAK,CAAC,4BAA4B,EAAEgB,QAAQ,CAACE,IAAI,CAACK,OAAO,CAAC;QAClE,MAAM,IAAIC,KAAK,CAACR,QAAQ,CAACE,IAAI,CAACK,OAAO,IAAI,2BAA2B,CAAC;MACzE;IACJ,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACZW,OAAO,CAACX,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACf;EACJ,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACZ,MAAM8B,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,IAAI,CAACF,MAAM,EAAE;MACTjB,QAAQ,CAAC,aAAa,CAAC;MACvB;IACJ;IAEA,MAAM6B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACAxB,QAAQ,CAAC,IAAI,CAAC;QAEd,MAAMyB,OAAO,GAAG,MAAMC,OAAO,CAACC,UAAU,CAAC,CACrClB,aAAa,CAAC,CAAC,EACfmB,aAAa,CAAC,CAAC,EACfC,qBAAqB,CAAC,CAAC,EACvBC,UAAU,CAAC,CAAC,CACf,CAAC;QAEFL,OAAO,CAACM,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;UAC/B,IAAID,MAAM,CAACE,MAAM,KAAK,UAAU,EAAE;YAC9BxB,OAAO,CAACX,KAAK,CAAC,oBAAoBkC,KAAK,GAAG,EAAED,MAAM,CAACG,MAAM,CAAC;UAC9D;QACJ,CAAC,CAAC;QAEFjC,WAAW,CAACkC,WAAW,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,OAAOC,GAAG,EAAE;QACV3B,OAAO,CAACX,KAAK,CAAC,iCAAiC,EAAEsC,GAAG,CAAC;QACrDrC,QAAQ,CAAC,wDAAwD,CAAC;MACtE;IACJ,CAAC;IAEDwB,mBAAmB,CAAC,CAAC;EACzB,CAAC,EAAE,CAAC7B,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhB,MAAMiC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACAlB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpC,MAAMC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MAC7C,MAAMC,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,4BAA4BJ,MAAM,EAAE,CAAC;MACtEF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEI,QAAQ,CAACE,IAAI,CAAC;MAEjD,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBd,aAAa,CAACW,QAAQ,CAACE,IAAI,CAACqB,IAAI,IAAI,EAAE,CAAC;MAC3C,CAAC,MAAM;QACH5B,OAAO,CAACX,KAAK,CAAC,4BAA4B,EAAEgB,QAAQ,CAACE,IAAI,CAACK,OAAO,CAAC;QAClE,MAAM,IAAIC,KAAK,CAACR,QAAQ,CAACE,IAAI,CAACK,OAAO,IAAI,2BAA2B,CAAC;MACzE;IACJ,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACZW,OAAO,CAACX,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACf;EACJ,CAAC;EAED,MAAM8B,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACAnB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,MAAMI,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,GAAG1B,YAAY,iCAAiC,CAAC;MAClFoB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEI,QAAQ,CAACE,IAAI,CAAC;MAEzD,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB,MAAMqB,kBAAkB,GAAG,CAACxB,QAAQ,CAACE,IAAI,CAACuB,UAAU,IAAI,EAAE,EAAEC,GAAG,CAACC,SAAS,KAAK;UAC1E,GAAGA,SAAS;UACZC,QAAQ,EAAE,IAAIC,IAAI,CAACF,SAAS,CAACC,QAAQ;QACzC,CAAC,CAAC,CAAC;QACHrC,mBAAmB,CAACiC,kBAAkB,CAAC;MAC3C,CAAC,MAAM;QACH7B,OAAO,CAACX,KAAK,CAAC,oCAAoC,EAAEgB,QAAQ,CAACE,IAAI,CAACK,OAAO,CAAC;QAC1E,MAAM,IAAIC,KAAK,CAACR,QAAQ,CAACE,IAAI,CAACK,OAAO,IAAI,mCAAmC,CAAC;MACjF;IACJ,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACZW,OAAO,CAACX,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK;IACf;EACJ,CAAC;EAED,MAAM+B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACApB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,MAAMI,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,GAAG1B,YAAY,+BAA+B,CAAC;MAChFoB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEI,QAAQ,CAACE,IAAI,CAAC;MAE7C,IAAIF,QAAQ,CAACE,IAAI,CAACiB,MAAM,KAAK,GAAG,EAAE;QAC9B1B,QAAQ,CAACO,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACtC,CAAC,MAAM;QACHP,OAAO,CAACX,KAAK,CAAC,wBAAwB,EAAEgB,QAAQ,CAACE,IAAI,CAACK,OAAO,CAAC;QAC9D,MAAM,IAAIC,KAAK,CAACR,QAAQ,CAACE,IAAI,CAACK,OAAO,IAAI,uBAAuB,CAAC;MACrE;IACJ,CAAC,CAAC,OAAOe,GAAG,EAAE;MACV3B,OAAO,CAACX,KAAK,CAAC,uBAAuB,EAAEsC,GAAG,CAAC;MAC3C,MAAMA,GAAG;IACb;EACJ,CAAC;EAED,MAAMQ,WAAW,GAAIC,QAAQ,IAAK;IAC9B,MAAMC,IAAI,GAAGxC,KAAK,CAACyC,IAAI,CAACD,IAAI,IAAIA,IAAI,CAACE,IAAI,KAAKH,QAAQ,CAAC;IACvD,OAAOC,IAAI,GAAG,GAAGzD,YAAY,IAAIyD,IAAI,CAACG,IAAI,EAAE,GAAG,EAAE;EACrD,CAAC;EAED,MAAMd,WAAW,GAAGA,CAAA,KAAM;IACtB,MAAMe,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACQ,QAAQ,CAAC,CAAC;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,cAAc;IACpC,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,cAAc;EACzB,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAC;IAAEC,IAAI;IAAEC,KAAK;IAAEC,OAAO;IAAEC,OAAO;IAAEC;EAAU,CAAC,KAAK;IACxE,IAAIA,SAAS,EAAE;MACX,oBAAOlE,OAAA;QAAMmE,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAChE;IACA,oBACIxE,OAAA;MAAAoE,QAAA,GACKN,IAAI,EAAC,IAAE,EAACC,KAAK,EAAC,IAAE,EAACC,OAAO,EAAC,IAAE,EAACC,OAAO,EAAC,GACzC;IAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAEf,CAAC;EAED,IAAIjE,KAAK,EAAE;IACP,oBACIP,OAAA;MAAKmE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3BpE,OAAA;QAAKmE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAE7D;MAAK;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC;EAEd;EAEA,oBACIxE,OAAA;IAAKmE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3BpE,OAAA;MAAAoE,QAAA,GAAK3D,QAAQ,EAAC,IAAE,EAACL,IAAI,CAACyB,QAAQ,IAAI,MAAM,EAAC,GAAC;IAAA;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/CxE,OAAA;MAAKmE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3BpE,OAAA;QAAKmE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BpE,OAAA;UAAAoE,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBxE,OAAA;UAAKmE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAM5BpE,OAAA;YAAMmE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAAC,GAAC,EAACK,UAAU,CAACrE,IAAI,CAACuB,OAAO,IAAI,CAAC,CAAC,CAAC+C,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,eACNxE,OAAA;UAAKmE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3BpE,OAAA,CAACR,IAAI;YAACmF,EAAE,EAAC,UAAU;YAACR,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtExE,OAAA,CAACR,IAAI;YAACmF,EAAE,EAAC,WAAW;YAACR,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENxE,OAAA;MAAKmE,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAClCpE,OAAA;QAAKmE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BpE,OAAA;UAAAoE,QAAA,EAAI;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpBxE,OAAA,CAACR,IAAI;UAACmF,EAAE,EAAC,YAAY;UAACR,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAC,WACrC,eAAApE,OAAA,CAACJ,YAAY;YAACuE,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNxE,OAAA;QAAKmE,SAAS,EAAC,WAAW;QAAAC,QAAA,EACrBzD,UAAU,CAACiE,MAAM,KAAK,CAAC,gBACpB5E,OAAA;UAAKmE,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GAEnD7D,UAAU,CAACsC,GAAG,CAAC4B,GAAG;UAAA,IAAAC,gBAAA;UAAA,oBACd9E,OAAA;YAAkDmE,SAAS,EAAC,UAAU;YAAAC,QAAA,gBAClEpE,OAAA;cAAKmE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBpE,OAAA;gBAAMmE,SAAS,EAAE,cAAcU,GAAG,CAACE,UAAU,EAAG;gBAAAX,QAAA,EAC3CS,GAAG,CAACE;cAAU;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACPxE,OAAA;gBAAMmE,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,OACvB,EAAC,EAAAU,gBAAA,GAAAD,GAAG,CAACG,WAAW,cAAAF,gBAAA,uBAAfA,gBAAA,CAAiBG,WAAW,CAAC,CAAC,KAAI,GAAGJ,GAAG,CAACK,MAAM,SAAS;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENxE,OAAA;cAAKmE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9BpE,OAAA;gBAAemE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAC,QACjC,EAACK,UAAU,CAACI,GAAG,CAACM,0BAA0B,IAAI,CAAC,CAAC,CAACT,OAAO,CAAC,CAAC,CAAC,EAAC,KACtE;cAAA,GAFS,KAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CAAC,eACNxE,OAAA;gBAAgBmE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAAC,SAClC,EAACK,UAAU,CAACI,GAAG,CAACO,2BAA2B,IAAI,CAAC,CAAC,CAACV,OAAO,CAAC,CAAC,CAAC,EAAC,KACxE;cAAA,GAFS,MAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CAAC,eACNxE,OAAA;gBAAgBmE,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAAC,SAClC,EAACK,UAAU,CAACI,GAAG,CAACQ,2BAA2B,IAAI,CAAC,CAAC,CAACX,OAAO,CAAC,CAAC,CAAC,EAAC,KACxE;cAAA,GAFS,MAAM;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENxE,OAAA;cAAKmE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBACxBpE,OAAA;gBAAKmE,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACrBpE,OAAA;kBAAKsF,GAAG,EAAEjC,WAAW,CAACwB,GAAG,CAACU,MAAM,CAAE;kBAACC,GAAG,EAAEX,GAAG,CAACU,MAAO;kBAACpB,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnFxE,OAAA;kBAAKmE,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBpE,OAAA;oBAAMmE,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAES,GAAG,CAACU;kBAAM;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/CxE,OAAA;oBAAMmE,SAAS,EAAC,UAAU;oBAAAC,QAAA,EACrBS,GAAG,CAACY,gBAAgB,KAAK,YAAY,GAAGZ,GAAG,CAACa,UAAU,GAAGb,GAAG,CAACc;kBAAU;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eACPxE,OAAA;oBAAMmE,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,GACxB,EAACK,UAAU,CAAC,CAACI,GAAG,CAACY,gBAAgB,KAAK,YAAY,GAAGZ,GAAG,CAACe,YAAY,GAAGf,GAAG,CAACgB,YAAY,KAAK,CAAC,CAAC,CAACnB,OAAO,CAAC,CAAC,CAAC,EAAC,KAChH;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENxE,OAAA;gBAAKmE,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAElCxE,OAAA;gBAAKmE,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACrBpE,OAAA;kBAAKsF,GAAG,EAAEjC,WAAW,CAACwB,GAAG,CAACiB,MAAM,CAAE;kBAACN,GAAG,EAAEX,GAAG,CAACiB,MAAO;kBAAC3B,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnFxE,OAAA;kBAAKmE,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBpE,OAAA;oBAAMmE,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAAES,GAAG,CAACiB;kBAAM;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/CxE,OAAA;oBAAMmE,SAAS,EAAC,UAAU;oBAAAC,QAAA,EACrBS,GAAG,CAACY,gBAAgB,KAAK,YAAY,GAAGZ,GAAG,CAACa,UAAU,GAAGb,GAAG,CAACc;kBAAU;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eACPxE,OAAA;oBAAMmE,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,GACxB,EAACK,UAAU,CAAC,CAACI,GAAG,CAACY,gBAAgB,KAAK,YAAY,GAAGZ,GAAG,CAACe,YAAY,GAAGf,GAAG,CAACgB,YAAY,KAAK,CAAC,CAAC,CAACnB,OAAO,CAAC,CAAC,CAAC,EAAC,KAChH;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA,GAlDAK,GAAG,CAACK,MAAM,IAAI,OAAOL,GAAG,CAACG,WAAW,EAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmD3C,CAAC;QAAA,CACT;MACJ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENxE,OAAA;MAAKmE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBACxCpE,OAAA;QAAKmE,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACrCpE,OAAA;UAAAoE,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BxE,OAAA,CAACR,IAAI;UAACmF,EAAE,EAAC,kBAAkB;UAACR,SAAS,EAAC,2BAA2B;UAAAC,QAAA,GAAC,WACrD,eAAApE,OAAA,CAACH,cAAc;YAACsE,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNxE,OAAA;QAAKmE,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EACrCvD,gBAAgB,CAAC+D,MAAM,KAAK,CAAC,gBAC1B5E,OAAA;UAAKmE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAEnExE,OAAA;UAAKmE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EACrCvD,gBAAgB,CAACkF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC9C,GAAG,CAAEC,SAAS,iBACxClD,OAAA;YAAiEmE,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACjGpE,OAAA;cAAKmE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACtCpE,OAAA;gBAAkBmE,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBAClDpE,OAAA;kBAAKmE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACxCpE,OAAA;oBACIsF,GAAG,EAAEjC,WAAW,CAACH,SAAS,CAACqC,MAAM,CAAE;oBACnCC,GAAG,EAAEtC,SAAS,CAACqC,MAAO;oBACtBpB,SAAS,EAAC,qBAAqB;oBAC/B6B,OAAO,EAAGC,CAAC,IAAK;sBACZA,CAAC,CAACC,MAAM,CAACZ,GAAG,GAAG,wBAAwB;oBAC3C;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNxE,OAAA;kBAAMmE,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAElB,SAAS,CAACqC;gBAAM;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAX1D,QAAQ;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYZ,CAAC,eACNxE,OAAA;gBAAKmE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5CxE,OAAA;gBAAkBmE,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBAClDpE,OAAA;kBAAKmE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACxCpE,OAAA;oBACIsF,GAAG,EAAEjC,WAAW,CAACH,SAAS,CAAC4C,MAAM,CAAE;oBACnCN,GAAG,EAAEtC,SAAS,CAAC4C,MAAO;oBACtB3B,SAAS,EAAC,qBAAqB;oBAC/B6B,OAAO,EAAGC,CAAC,IAAK;sBACZA,CAAC,CAACC,MAAM,CAACZ,GAAG,GAAG,wBAAwB;oBAC3C;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNxE,OAAA;kBAAMmE,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAElB,SAAS,CAAC4C;gBAAM;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAX1D,QAAQ;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNxE,OAAA;cAAKmE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBACxCpE,OAAA;gBAAKmE,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACrCpE,OAAA;kBAAKmE,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACtCpE,OAAA;oBAAKmE,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBAC/BpE,OAAA;sBAAMmE,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrDxE,OAAA;sBAAMmE,SAAS,EAAE,+CAA+CjB,SAAS,CAACR,MAAM,CAACyD,WAAW,CAAC,CAAC,EAAG;sBAAA/B,QAAA,EAC5FlB,SAAS,CAACR;oBAAM;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNxE,OAAA;oBAAKmE,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBAC/BpE,OAAA;sBAAMmE,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7DxE,OAAA;sBAAMmE,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,GAAC,GAAC,EAAClB,SAAS,CAACkD,qBAAqB,EAAC,MAAI,EAAClD,SAAS,CAACmD,qBAAqB;oBAAA;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpH,CAAC,eACNxE,OAAA;oBAAKmE,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBAC/BpE,OAAA;sBAAMmE,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnDxE,OAAA;sBAAMmE,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,GAAElB,SAAS,CAACoD,WAAW,EAAC,KAAG,EAACpD,SAAS,CAACqD,WAAW;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNxE,OAAA;kBAAMmE,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EACrC,IAAIhB,IAAI,CAACF,SAAS,CAACsD,UAAU,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACNxE,OAAA;gBAAKmE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACtCpE,OAAA,CAACN,SAAS;kBACNgH,IAAI,EAAExD,SAAS,CAACC,QAAS;kBACzBwD,QAAQ,EAAE9C;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNxE,OAAA,CAACR,IAAI;gBACDmF,EAAE,EAAE,wBAAwBzB,SAAS,CAAC0D,YAAY,EAAG;gBACrDzC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EACzC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GAhEAtB,SAAS,CAAC0D,YAAY,IAAI,aAAa1D,SAAS,CAAC2D,EAAE,EAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiE1D,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACtE,EAAA,CA9UQD,aAAa;EAAA,QACDR,WAAW,EAEJE,OAAO;AAAA;AAAAmH,EAAA,GAH1B7G,aAAa;AAgVtB,eAAeA,aAAa;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}