{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserSettingsSimple.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport UserLayout from '../components/UserLayout';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\nfunction UserSettingsSimple() {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [userId, setUserId] = useState(null);\n  const [userEmail, setUserEmail] = useState('');\n  const [securitySettings, setSecuritySettings] = useState({\n    otp_enabled: false,\n    tfa_enabled: false,\n    auth_method: 'password_only'\n  });\n  useEffect(() => {\n    const storedUserId = localStorage.getItem('userId');\n    if (!storedUserId) {\n      navigate('/user/login');\n      return;\n    }\n    setUserId(storedUserId);\n    loadUserSettings();\n  }, [navigate]);\n  const loadUserSettings = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const userId = localStorage.getItem('userId');\n      console.log('Loading settings for user:', userId);\n\n      // Load user security settings\n      const securityResponse = await axios.get(`${API_BASE_URL}/handlers/user_security_settings.php?userId=${userId}`);\n      console.log('Security response:', securityResponse.data);\n      if (securityResponse.data.success) {\n        setSecuritySettings(securityResponse.data.settings);\n        setUserEmail(securityResponse.data.user_email);\n        setSuccess('Settings loaded successfully!');\n      } else {\n        setError('Failed to load security settings');\n      }\n    } catch (err) {\n      console.error('Settings load error:', err);\n      setError('Failed to load settings. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleOtpToggle = async () => {\n    try {\n      setError('');\n      const newOtpState = !securitySettings.otp_enabled;\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_toggle_otp.php`, {\n        userId: userId,\n        enabled: newOtpState\n      });\n      if (response.data.success) {\n        setSecuritySettings(prev => ({\n          ...prev,\n          otp_enabled: newOtpState\n        }));\n        setSuccess(`OTP ${newOtpState ? 'enabled' : 'disabled'} successfully`);\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update OTP setting');\n      }\n    } catch (err) {\n      setError('Failed to update OTP setting. Please try again.');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(UserLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '20px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Loading settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(UserLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '20px',\n        maxWidth: '800px',\n        margin: '0 auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDD27 User Settings (Simple Test)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage your account security and preferences\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#fed7d7',\n          color: '#c53030',\n          padding: '12px',\n          borderRadius: '8px',\n          marginBottom: '20px'\n        },\n        children: [\"\\u274C \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 21\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#c6f6d5',\n          color: '#2f855a',\n          padding: '12px',\n          borderRadius: '8px',\n          marginBottom: '20px'\n        },\n        children: [\"\\u2705 \", success]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: 'white',\n          padding: '20px',\n          borderRadius: '8px',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDD10 Security Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"User: \", userEmail]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"User ID: \", userId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCE7 Email OTP (One-Time Password)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Current status: \", securitySettings.otp_enabled ? '✅ Enabled' : '❌ Disabled']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleOtpToggle,\n            style: {\n              backgroundColor: securitySettings.otp_enabled ? '#e53e3e' : '#38a169',\n              color: 'white',\n              padding: '10px 20px',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'pointer'\n            },\n            children: securitySettings.otp_enabled ? 'Disable OTP' : 'Enable OTP'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDD10 Two-Factor Authentication (2FA)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Current status: \", securitySettings.tfa_enabled ? '✅ Enabled' : '❌ Disabled']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Auth Method: \", securitySettings.auth_method]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            disabled: true,\n            style: {\n              backgroundColor: '#a0aec0',\n              color: 'white',\n              padding: '10px 20px',\n              border: 'none',\n              borderRadius: '6px',\n              cursor: 'not-allowed'\n            },\n            children: \"Setup 2FA (Coming Soon)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f7fafc',\n          padding: '20px',\n          borderRadius: '8px',\n          border: '1px solid #e2e8f0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u2139\\uFE0F Debug Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            fontSize: '12px',\n            overflow: 'auto'\n          },\n          children: JSON.stringify({\n            userId,\n            userEmail,\n            securitySettings\n          }, null, 2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 9\n  }, this);\n}\n_s(UserSettingsSimple, \"HeQXaDbwezWzWtQYSzlqHqjTgPc=\", false, function () {\n  return [useNavigate];\n});\n_c = UserSettingsSimple;\nexport default UserSettingsSimple;\nvar _c;\n$RefreshReg$(_c, \"UserSettingsSimple\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "axios", "UserLayout", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "UserSettingsSimple", "_s", "navigate", "loading", "setLoading", "error", "setError", "success", "setSuccess", "userId", "setUserId", "userEmail", "setUserEmail", "securitySettings", "setSecuritySettings", "otp_enabled", "tfa_enabled", "auth_method", "storedUserId", "localStorage", "getItem", "loadUserSettings", "console", "log", "securityResponse", "get", "data", "settings", "user_email", "err", "handleOtpToggle", "newOtpState", "response", "post", "enabled", "prev", "setTimeout", "message", "children", "style", "padding", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "margin", "backgroundColor", "color", "borderRadius", "marginBottom", "boxShadow", "onClick", "border", "cursor", "disabled", "fontSize", "overflow", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserSettingsSimple.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport UserLayout from '../components/UserLayout';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\n\nfunction UserSettingsSimple() {\n    const navigate = useNavigate();\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [userId, setUserId] = useState(null);\n    const [userEmail, setUserEmail] = useState('');\n    const [securitySettings, setSecuritySettings] = useState({\n        otp_enabled: false,\n        tfa_enabled: false,\n        auth_method: 'password_only'\n    });\n\n    useEffect(() => {\n        const storedUserId = localStorage.getItem('userId');\n        if (!storedUserId) {\n            navigate('/user/login');\n            return;\n        }\n        \n        setUserId(storedUserId);\n        loadUserSettings();\n    }, [navigate]);\n\n    const loadUserSettings = async () => {\n        try {\n            setLoading(true);\n            setError('');\n\n            const userId = localStorage.getItem('userId');\n            console.log('Loading settings for user:', userId);\n            \n            // Load user security settings\n            const securityResponse = await axios.get(\n                `${API_BASE_URL}/handlers/user_security_settings.php?userId=${userId}`\n            );\n\n            console.log('Security response:', securityResponse.data);\n\n            if (securityResponse.data.success) {\n                setSecuritySettings(securityResponse.data.settings);\n                setUserEmail(securityResponse.data.user_email);\n                setSuccess('Settings loaded successfully!');\n            } else {\n                setError('Failed to load security settings');\n            }\n\n        } catch (err) {\n            console.error('Settings load error:', err);\n            setError('Failed to load settings. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleOtpToggle = async () => {\n        try {\n            setError('');\n            const newOtpState = !securitySettings.otp_enabled;\n            \n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/user_toggle_otp.php`,\n                {\n                    userId: userId,\n                    enabled: newOtpState\n                }\n            );\n\n            if (response.data.success) {\n                setSecuritySettings(prev => ({\n                    ...prev,\n                    otp_enabled: newOtpState\n                }));\n                setSuccess(`OTP ${newOtpState ? 'enabled' : 'disabled'} successfully`);\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to update OTP setting');\n            }\n        } catch (err) {\n            setError('Failed to update OTP setting. Please try again.');\n        }\n    };\n\n    if (loading) {\n        return (\n            <UserLayout>\n                <div style={{ padding: '20px', textAlign: 'center' }}>\n                    <h2>Loading settings...</h2>\n                </div>\n            </UserLayout>\n        );\n    }\n\n    return (\n        <UserLayout>\n            <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>\n                <h1>🔧 User Settings (Simple Test)</h1>\n                <p>Manage your account security and preferences</p>\n\n                {error && (\n                    <div style={{ \n                        backgroundColor: '#fed7d7', \n                        color: '#c53030', \n                        padding: '12px', \n                        borderRadius: '8px', \n                        marginBottom: '20px' \n                    }}>\n                        ❌ {error}\n                    </div>\n                )}\n\n                {success && (\n                    <div style={{ \n                        backgroundColor: '#c6f6d5', \n                        color: '#2f855a', \n                        padding: '12px', \n                        borderRadius: '8px', \n                        marginBottom: '20px' \n                    }}>\n                        ✅ {success}\n                    </div>\n                )}\n\n                <div style={{ \n                    backgroundColor: 'white', \n                    padding: '20px', \n                    borderRadius: '8px', \n                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                    marginBottom: '20px'\n                }}>\n                    <h2>🔐 Security Settings</h2>\n                    <p>User: {userEmail}</p>\n                    <p>User ID: {userId}</p>\n                    \n                    <div style={{ marginBottom: '20px' }}>\n                        <h3>📧 Email OTP (One-Time Password)</h3>\n                        <p>Current status: {securitySettings.otp_enabled ? '✅ Enabled' : '❌ Disabled'}</p>\n                        <button \n                            onClick={handleOtpToggle}\n                            style={{\n                                backgroundColor: securitySettings.otp_enabled ? '#e53e3e' : '#38a169',\n                                color: 'white',\n                                padding: '10px 20px',\n                                border: 'none',\n                                borderRadius: '6px',\n                                cursor: 'pointer'\n                            }}\n                        >\n                            {securitySettings.otp_enabled ? 'Disable OTP' : 'Enable OTP'}\n                        </button>\n                    </div>\n\n                    <div style={{ marginBottom: '20px' }}>\n                        <h3>🔐 Two-Factor Authentication (2FA)</h3>\n                        <p>Current status: {securitySettings.tfa_enabled ? '✅ Enabled' : '❌ Disabled'}</p>\n                        <p>Auth Method: {securitySettings.auth_method}</p>\n                        <button \n                            disabled\n                            style={{\n                                backgroundColor: '#a0aec0',\n                                color: 'white',\n                                padding: '10px 20px',\n                                border: 'none',\n                                borderRadius: '6px',\n                                cursor: 'not-allowed'\n                            }}\n                        >\n                            Setup 2FA (Coming Soon)\n                        </button>\n                    </div>\n                </div>\n\n                <div style={{ \n                    backgroundColor: '#f7fafc', \n                    padding: '20px', \n                    borderRadius: '8px',\n                    border: '1px solid #e2e8f0'\n                }}>\n                    <h3>ℹ️ Debug Information</h3>\n                    <pre style={{ fontSize: '12px', overflow: 'auto' }}>\n                        {JSON.stringify({ userId, userEmail, securitySettings }, null, 2)}\n                    </pre>\n                </div>\n            </div>\n        </UserLayout>\n    );\n}\n\nexport default UserSettingsSimple;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,oCAAoC;AAE/F,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC;IACrD0B,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE;EACjB,CAAC,CAAC;EAEF3B,SAAS,CAAC,MAAM;IACZ,MAAM4B,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACnD,IAAI,CAACF,YAAY,EAAE;MACfhB,QAAQ,CAAC,aAAa,CAAC;MACvB;IACJ;IAEAQ,SAAS,CAACQ,YAAY,CAAC;IACvBG,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACnB,QAAQ,CAAC,CAAC;EAEd,MAAMmB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACAjB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMG,MAAM,GAAGU,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MAC7CE,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEd,MAAM,CAAC;;MAEjD;MACA,MAAMe,gBAAgB,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CACpC,GAAG7B,YAAY,+CAA+Ca,MAAM,EACxE,CAAC;MAEDa,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,gBAAgB,CAACE,IAAI,CAAC;MAExD,IAAIF,gBAAgB,CAACE,IAAI,CAACnB,OAAO,EAAE;QAC/BO,mBAAmB,CAACU,gBAAgB,CAACE,IAAI,CAACC,QAAQ,CAAC;QACnDf,YAAY,CAACY,gBAAgB,CAACE,IAAI,CAACE,UAAU,CAAC;QAC9CpB,UAAU,CAAC,+BAA+B,CAAC;MAC/C,CAAC,MAAM;QACHF,QAAQ,CAAC,kCAAkC,CAAC;MAChD;IAEJ,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACVP,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEwB,GAAG,CAAC;MAC1CvB,QAAQ,CAAC,4CAA4C,CAAC;IAC1D,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM0B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACAxB,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMyB,WAAW,GAAG,CAAClB,gBAAgB,CAACE,WAAW;MAEjD,MAAMiB,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,IAAI,CAC7B,GAAGrC,YAAY,+BAA+B,EAC9C;QACIa,MAAM,EAAEA,MAAM;QACdyB,OAAO,EAAEH;MACb,CACJ,CAAC;MAED,IAAIC,QAAQ,CAACN,IAAI,CAACnB,OAAO,EAAE;QACvBO,mBAAmB,CAACqB,IAAI,KAAK;UACzB,GAAGA,IAAI;UACPpB,WAAW,EAAEgB;QACjB,CAAC,CAAC,CAAC;QACHvB,UAAU,CAAC,OAAOuB,WAAW,GAAG,SAAS,GAAG,UAAU,eAAe,CAAC;QACtEK,UAAU,CAAC,MAAM5B,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC0B,QAAQ,CAACN,IAAI,CAACW,OAAO,IAAI,8BAA8B,CAAC;MACrE;IACJ,CAAC,CAAC,OAAOR,GAAG,EAAE;MACVvB,QAAQ,CAAC,iDAAiD,CAAC;IAC/D;EACJ,CAAC;EAED,IAAIH,OAAO,EAAE;IACT,oBACIR,OAAA,CAACF,UAAU;MAAA6C,QAAA,eACP3C,OAAA;QAAK4C,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAH,QAAA,eACjD3C,OAAA;UAAA2C,QAAA,EAAI;QAAmB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAErB;EAEA,oBACIlD,OAAA,CAACF,UAAU;IAAA6C,QAAA,eACP3C,OAAA;MAAK4C,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEM,QAAQ,EAAE,OAAO;QAAEC,MAAM,EAAE;MAAS,CAAE;MAAAT,QAAA,gBACjE3C,OAAA;QAAA2C,QAAA,EAAI;MAA8B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvClD,OAAA;QAAA2C,QAAA,EAAG;MAA4C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAElDxC,KAAK,iBACFV,OAAA;QAAK4C,KAAK,EAAE;UACRS,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,SAAS;UAChBT,OAAO,EAAE,MAAM;UACfU,YAAY,EAAE,KAAK;UACnBC,YAAY,EAAE;QAClB,CAAE;QAAAb,QAAA,GAAC,SACG,EAACjC,KAAK;MAAA;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACR,EAEAtC,OAAO,iBACJZ,OAAA;QAAK4C,KAAK,EAAE;UACRS,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,SAAS;UAChBT,OAAO,EAAE,MAAM;UACfU,YAAY,EAAE,KAAK;UACnBC,YAAY,EAAE;QAClB,CAAE;QAAAb,QAAA,GAAC,SACG,EAAC/B,OAAO;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACR,eAEDlD,OAAA;QAAK4C,KAAK,EAAE;UACRS,eAAe,EAAE,OAAO;UACxBR,OAAO,EAAE,MAAM;UACfU,YAAY,EAAE,KAAK;UACnBE,SAAS,EAAE,2BAA2B;UACtCD,YAAY,EAAE;QAClB,CAAE;QAAAb,QAAA,gBACE3C,OAAA;UAAA2C,QAAA,EAAI;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BlD,OAAA;UAAA2C,QAAA,GAAG,QAAM,EAAC3B,SAAS;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBlD,OAAA;UAAA2C,QAAA,GAAG,WAAS,EAAC7B,MAAM;QAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAExBlD,OAAA;UAAK4C,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAO,CAAE;UAAAb,QAAA,gBACjC3C,OAAA;YAAA2C,QAAA,EAAI;UAAgC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzClD,OAAA;YAAA2C,QAAA,GAAG,kBAAgB,EAACzB,gBAAgB,CAACE,WAAW,GAAG,WAAW,GAAG,YAAY;UAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFlD,OAAA;YACI0D,OAAO,EAAEvB,eAAgB;YACzBS,KAAK,EAAE;cACHS,eAAe,EAAEnC,gBAAgB,CAACE,WAAW,GAAG,SAAS,GAAG,SAAS;cACrEkC,KAAK,EAAE,OAAO;cACdT,OAAO,EAAE,WAAW;cACpBc,MAAM,EAAE,MAAM;cACdJ,YAAY,EAAE,KAAK;cACnBK,MAAM,EAAE;YACZ,CAAE;YAAAjB,QAAA,EAEDzB,gBAAgB,CAACE,WAAW,GAAG,aAAa,GAAG;UAAY;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAENlD,OAAA;UAAK4C,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAO,CAAE;UAAAb,QAAA,gBACjC3C,OAAA;YAAA2C,QAAA,EAAI;UAAkC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3ClD,OAAA;YAAA2C,QAAA,GAAG,kBAAgB,EAACzB,gBAAgB,CAACG,WAAW,GAAG,WAAW,GAAG,YAAY;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFlD,OAAA;YAAA2C,QAAA,GAAG,eAAa,EAACzB,gBAAgB,CAACI,WAAW;UAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDlD,OAAA;YACI6D,QAAQ;YACRjB,KAAK,EAAE;cACHS,eAAe,EAAE,SAAS;cAC1BC,KAAK,EAAE,OAAO;cACdT,OAAO,EAAE,WAAW;cACpBc,MAAM,EAAE,MAAM;cACdJ,YAAY,EAAE,KAAK;cACnBK,MAAM,EAAE;YACZ,CAAE;YAAAjB,QAAA,EACL;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENlD,OAAA;QAAK4C,KAAK,EAAE;UACRS,eAAe,EAAE,SAAS;UAC1BR,OAAO,EAAE,MAAM;UACfU,YAAY,EAAE,KAAK;UACnBI,MAAM,EAAE;QACZ,CAAE;QAAAhB,QAAA,gBACE3C,OAAA;UAAA2C,QAAA,EAAI;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BlD,OAAA;UAAK4C,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAApB,QAAA,EAC9CqB,IAAI,CAACC,SAAS,CAAC;YAAEnD,MAAM;YAAEE,SAAS;YAAEE;UAAiB,CAAC,EAAE,IAAI,EAAE,CAAC;QAAC;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB;AAAC5C,EAAA,CA1LQD,kBAAkB;EAAA,QACNT,WAAW;AAAA;AAAAsE,EAAA,GADvB7D,kBAAkB;AA4L3B,eAAeA,kBAAkB;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}