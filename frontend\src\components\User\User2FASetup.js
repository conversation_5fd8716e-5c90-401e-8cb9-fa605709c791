import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { 
    FaQrc<PERSON>, 
    <PERSON>a<PERSON><PERSON>, 
    <PERSON>a<PERSON><PERSON>, 
    FaCheck, 
    FaArrowLeft, 
    FaShieldAlt,
    FaExclamationTriangle,
    FaInfoCircle
} from 'react-icons/fa';
import './User2FASetup.css';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';

const User2FASetup = ({ userId, userEmail, onSuccess, onBack }) => {
    const [step, setStep] = useState(1); // 1: Setup, 2: Verify, 3: Backup Codes
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    
    // Setup data
    const [secretKey, setSecretKey] = useState('');
    const [qrCodeUrl, setQrCodeUrl] = useState('');
    const [backupCodes, setBackupCodes] = useState([]);
    const [manualEntryKey, setManualEntryKey] = useState('');
    
    // Verification
    const [verificationCode, setVerificationCode] = useState('');
    const [verifying, setVerifying] = useState(false);
    
    // UI state
    const [copied, setCopied] = useState(false);
    const [backupCodesCopied, setBackupCodesCopied] = useState(false);

    useEffect(() => {
        initiate2FASetup();
    }, []);

    const initiate2FASetup = async () => {
        try {
            setLoading(true);
            setError('');

            const response = await axios.get(`${API_BASE_URL}/handlers/user_setup_2fa.php?userId=${userId}`);

            if (response.data.success) {
                setSecretKey(response.data.secret_key);
                setQrCodeUrl(response.data.qr_code_url);
                setBackupCodes(response.data.backup_codes);
                setManualEntryKey(response.data.manual_entry_key);
                setSuccess('2FA setup initiated successfully');
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to initiate 2FA setup');
            }
        } catch (err) {
            setError('Failed to initiate 2FA setup. Please try again.');
            console.error('2FA setup error:', err);
        } finally {
            setLoading(false);
        }
    };

    const verify2FASetup = async () => {
        if (!verificationCode || verificationCode.length !== 6) {
            setError('Please enter a valid 6-digit code from your authenticator app');
            return;
        }

        try {
            setVerifying(true);
            setError('');

            const response = await axios.post(`${API_BASE_URL}/handlers/user_setup_2fa.php?userId=${userId}`, {
                verification_code: verificationCode
            });

            if (response.data.success) {
                setSuccess('2FA setup completed successfully!');
                setStep(3); // Show backup codes
            } else {
                setError(response.data.message || 'Invalid verification code');
            }
        } catch (err) {
            setError('Failed to verify 2FA setup. Please try again.');
            console.error('2FA verification error:', err);
        } finally {
            setVerifying(false);
        }
    };

    const copyToClipboard = async (text, type = 'secret') => {
        try {
            await navigator.clipboard.writeText(text);
            if (type === 'secret') {
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
            } else if (type === 'backup') {
                setBackupCodesCopied(true);
                setTimeout(() => setBackupCodesCopied(false), 2000);
            }
        } catch (err) {
            console.error('Failed to copy to clipboard:', err);
        }
    };

    const handleComplete = () => {
        onSuccess();
    };

    if (loading) {
        return (
            <div className="user-2fa-setup">
                <div className="setup-loading">
                    <div className="loading-spinner"></div>
                    <p>Setting up 2FA...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="user-2fa-setup">
            <div className="setup-header">
                <button onClick={onBack} className="back-btn">
                    <FaArrowLeft /> Back to Settings
                </button>
                <h1><FaShieldAlt /> Two-Factor Authentication Setup</h1>
                <div className="setup-steps">
                    <div className={`step ${step >= 1 ? 'active' : ''}`}>1</div>
                    <div className={`step ${step >= 2 ? 'active' : ''}`}>2</div>
                    <div className={`step ${step >= 3 ? 'active' : ''}`}>3</div>
                </div>
            </div>

            {error && (
                <div className="setup-alert setup-alert-error">
                    <FaExclamationTriangle />
                    <span>{error}</span>
                </div>
            )}

            {success && (
                <div className="setup-alert setup-alert-success">
                    <FaCheck />
                    <span>{success}</span>
                </div>
            )}

            {step === 1 && (
                <div className="setup-step">
                    <div className="step-header">
                        <h2><FaQrcode /> Step 1: Scan QR Code</h2>
                        <p>Use Google Authenticator or any compatible 2FA app to scan this QR code</p>
                    </div>

                    <div className="setup-content">
                        <div className="qr-section">
                            <div className="qr-code-container">
                                {qrCodeUrl && (
                                    <img src={qrCodeUrl} alt="2FA QR Code" className="qr-code" />
                                )}
                            </div>
                            
                            <div className="manual-entry">
                                <h3><FaKey /> Manual Entry</h3>
                                <p>If you can't scan the QR code, enter this key manually:</p>
                                <div className="secret-key-container">
                                    <code className="secret-key">{manualEntryKey}</code>
                                    <button
                                        onClick={() => copyToClipboard(manualEntryKey, 'secret')}
                                        className="copy-btn"
                                        title="Copy to clipboard"
                                    >
                                        {copied ? <FaCheck /> : <FaCopy />}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="setup-info">
                            <div className="info-card">
                                <FaInfoCircle className="info-icon" />
                                <div>
                                    <h4>Setup Instructions:</h4>
                                    <ol>
                                        <li>Download Google Authenticator or similar app</li>
                                        <li>Scan the QR code or enter the key manually</li>
                                        <li>Your app will generate 6-digit codes</li>
                                        <li>Enter a code in the next step to verify</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="step-actions">
                        <button
                            onClick={() => setStep(2)}
                            className="btn btn-primary"
                            disabled={!qrCodeUrl}
                        >
                            Next: Verify Setup
                        </button>
                    </div>
                </div>
            )}

            {step === 2 && (
                <div className="setup-step">
                    <div className="step-header">
                        <h2><FaCheck /> Step 2: Verify Setup</h2>
                        <p>Enter the 6-digit code from your authenticator app to verify the setup</p>
                    </div>

                    <div className="setup-content">
                        <div className="verification-form">
                            <div className="form-group">
                                <label htmlFor="verificationCode">Verification Code</label>
                                <input
                                    type="text"
                                    id="verificationCode"
                                    value={verificationCode}
                                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                                    placeholder="Enter 6-digit code"
                                    className="verification-input"
                                    maxLength="6"
                                />
                            </div>
                        </div>
                    </div>

                    <div className="step-actions">
                        <button
                            onClick={() => setStep(1)}
                            className="btn btn-secondary"
                        >
                            Back
                        </button>
                        <button
                            onClick={verify2FASetup}
                            className="btn btn-primary"
                            disabled={verifying || verificationCode.length !== 6}
                        >
                            {verifying ? 'Verifying...' : 'Verify & Complete'}
                        </button>
                    </div>
                </div>
            )}

            {step === 3 && (
                <div className="setup-step">
                    <div className="step-header">
                        <h2><FaShieldAlt /> Step 3: Backup Codes</h2>
                        <p>Save these backup codes in a secure location. You can use them if you lose access to your authenticator app.</p>
                    </div>

                    <div className="setup-content">
                        <div className="backup-codes-section">
                            <div className="backup-codes-container">
                                <div className="backup-codes-header">
                                    <h3>Your Backup Codes</h3>
                                    <button
                                        onClick={() => copyToClipboard(backupCodes.join('\n'), 'backup')}
                                        className="copy-btn"
                                        title="Copy all codes"
                                    >
                                        {backupCodesCopied ? <FaCheck /> : <FaCopy />} Copy All
                                    </button>
                                </div>
                                <div className="backup-codes-grid">
                                    {backupCodes.map((code, index) => (
                                        <code key={index} className="backup-code">{code}</code>
                                    ))}
                                </div>
                            </div>

                            <div className="backup-warning">
                                <FaExclamationTriangle className="warning-icon" />
                                <div>
                                    <strong>Important:</strong> Store these codes securely. Each code can only be used once. 
                                    If you lose both your authenticator app and these codes, you may lose access to your account.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="step-actions">
                        <button
                            onClick={handleComplete}
                            className="btn btn-success"
                        >
                            <FaCheck /> Complete Setup
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default User2FASetup;
