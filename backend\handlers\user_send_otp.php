<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';
require_once '../vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Function to send email using SMTP settings
function sendEmail($to, $subject, $message, $conn) {
    // Get SMTP settings
    $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        return [
            'success' => false,
            'message' => 'SMTP settings not configured'
        ];
    }
    
    $smtp = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Set up PHPMailer
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtp['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $smtp['username'];
        $mail->Password = $smtp['password'];
        $mail->SMTPSecure = $smtp['encryption'] === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = $smtp['port'];
        
        // Recipients
        $mail->setFrom($smtp['from_email'], $smtp['from_name']);
        $mail->addAddress($to);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $message;
        
        $mail->send();
        return ['success' => true, 'message' => 'Email sent successfully'];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to send email: ' . $mail->ErrorInfo
        ];
    }
}

try {
    $conn = getDBConnection();
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Only POST method is allowed");
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $userId = $input['userId'] ?? null;
    $email = $input['email'] ?? null;
    
    if (!$userId || !$email) {
        throw new Exception("User ID and email are required");
    }
    
    // Verify user exists and has OTP enabled
    $stmt = $conn->prepare("SELECT user_id, username, email, otp_enabled FROM users WHERE user_id = ? AND email = ?");
    $stmt->execute([$userId, $email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("User not found or email mismatch");
    }
    
    if (!$user['otp_enabled']) {
        throw new Exception("OTP is not enabled for this user");
    }
    
    // Check for rate limiting (max 3 OTP requests per 15 minutes)
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM user_otp 
        WHERE user_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
    ");
    $stmt->execute([$userId]);
    $recentOtpCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($recentOtpCount >= 3) {
        throw new Exception("Too many OTP requests. Please wait 15 minutes before requesting another code.");
    }
    
    // Check if user is locked out
    $stmt = $conn->prepare("
        SELECT locked_until 
        FROM user_otp 
        WHERE user_id = ? AND locked_until > NOW() 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$userId]);
    $lockout = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($lockout) {
        throw new Exception("Account is temporarily locked due to too many failed OTP attempts. Please try again later.");
    }
    
    $conn->beginTransaction();
    
    // Generate 6-digit OTP
    $otp = sprintf('%06d', mt_rand(0, 999999));
    
    // Set expiry time (5 minutes from now)
    $expiresAt = date('Y-m-d H:i:s', time() + 300);
    
    // Store OTP in database
    $stmt = $conn->prepare("
        INSERT INTO user_otp (user_id, otp, expires_at) 
        VALUES (?, ?, ?)
    ");
    $stmt->execute([$userId, $otp, $expiresAt]);
    
    // Prepare email content
    $subject = 'FanBet247 - Login Verification Code';
    $message = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #52B788; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .otp-code { font-size: 24px; font-weight: bold; color: #52B788; text-align: center; padding: 20px; background-color: white; border: 2px solid #52B788; border-radius: 8px; margin: 20px 0; }
            .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>FanBet247</h1>
                <h2>Login Verification</h2>
            </div>
            <div class='content'>
                <p>Hello {$user['username']},</p>
                <p>You have requested to log in to your FanBet247 account. Please use the verification code below to complete your login:</p>
                <div class='otp-code'>{$otp}</div>
                <p><strong>This code will expire in 5 minutes.</strong></p>
                <p>If you did not request this code, please ignore this email or contact our support team.</p>
                <p>For your security, never share this code with anyone.</p>
            </div>
            <div class='footer'>
                <p>© 2024 FanBet247. All rights reserved.</p>
                <p>This is an automated message, please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    // Send email
    $emailResult = sendEmail($user['email'], $subject, $message, $conn);
    
    if (!$emailResult['success']) {
        throw new Exception($emailResult['message']);
    }
    
    // Log OTP send
    $stmt = $conn->prepare("
        INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, 'otp', 'otp_sent', ?, ?, ?)
    ");
    $stmt->execute([
        $userId,
        json_encode(['email' => $user['email'], 'expires_at' => $expiresAt]),
        $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);
    
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'OTP sent successfully to your email address',
        'expires_in' => 300 // 5 minutes in seconds
    ]);
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error occurred'
    ]);
}
?>
