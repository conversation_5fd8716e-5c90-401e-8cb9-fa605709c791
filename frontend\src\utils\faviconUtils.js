/**
 * Utility functions for managing favicon dynamically
 */

/**
 * Updates the favicon in the document head
 * @param {string} faviconUrl - The URL of the new favicon
 */
export const updateFavicon = (faviconUrl) => {
    try {
        // Remove existing favicon links
        const existingFavicons = document.querySelectorAll('link[rel*="icon"]');
        existingFavicons.forEach(link => link.remove());

        // Create new favicon link
        const link = document.createElement('link');
        link.rel = 'icon';
        link.type = 'image/x-icon';
        link.href = faviconUrl;
        
        // Add to document head
        document.head.appendChild(link);

        // Also update apple-touch-icon if it exists
        const appleTouchIcon = document.querySelector('link[rel="apple-touch-icon"]');
        if (appleTouchIcon && faviconUrl.endsWith('.png')) {
            appleTouchIcon.href = faviconUrl;
        }

        console.log('Favicon updated successfully:', faviconUrl);
        return true;
    } catch (error) {
        console.error('Error updating favicon:', error);
        return false;
    }
};

/**
 * Fetches the current favicon from the server and updates it
 */
export const refreshFavicon = async () => {
    try {
        const response = await fetch('/backend/handlers/get_favicon.php');
        const data = await response.json();
        
        if (data.success && data.favicon_url) {
            updateFavicon(data.favicon_url);
            return data.favicon_url;
        } else if (data.public_favicon_exists) {
            updateFavicon('/favicon.ico');
            return '/favicon.ico';
        }
        
        return null;
    } catch (error) {
        console.error('Error refreshing favicon:', error);
        return null;
    }
};

/**
 * Preloads a favicon to ensure it's cached
 * @param {string} faviconUrl - The URL of the favicon to preload
 */
export const preloadFavicon = (faviconUrl) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = faviconUrl;
    document.head.appendChild(link);
};

/**
 * Validates if a file is a valid favicon format
 * @param {File} file - The file to validate
 * @returns {Object} - Validation result with success and message
 */
export const validateFaviconFile = (file) => {
    const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/ico', 'image/icon', 'image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];
    const allowedExtensions = ['ico', 'png', 'jpg', 'jpeg', 'svg'];
    const fileExtension = file.name.split('.').pop().toLowerCase();
    
    // Check file type
    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
        return {
            success: false,
            message: 'Please select a valid favicon file (ICO, PNG, JPG, or SVG)'
        };
    }
    
    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
        return {
            success: false,
            message: 'Favicon file size must be less than 2MB'
        };
    }
    
    // Check dimensions for optimal favicon (optional warning)
    return new Promise((resolve) => {
        const img = new Image();
        img.onload = function() {
            const optimalSizes = [16, 32, 48, 64];
            const isOptimalSize = optimalSizes.includes(this.width) && this.width === this.height;
            
            resolve({
                success: true,
                message: isOptimalSize ? 'Valid favicon file' : 'File is valid, but optimal favicon sizes are 16x16, 32x32, or 48x48 pixels',
                isOptimalSize
            });
        };
        img.onerror = function() {
            resolve({
                success: true,
                message: 'Valid favicon file (unable to check dimensions)'
            });
        };
        img.src = URL.createObjectURL(file);
    });
};

/**
 * Gets the current favicon URL from the document
 * @returns {string|null} - The current favicon URL or null if not found
 */
export const getCurrentFaviconUrl = () => {
    const faviconLink = document.querySelector('link[rel*="icon"]');
    return faviconLink ? faviconLink.href : null;
};
