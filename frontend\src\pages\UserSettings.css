.user-settings {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.settings-header {
    margin-bottom: 32px;
}

.settings-header h1 {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 28px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 8px 0;
}

.settings-header p {
    color: #718096;
    font-size: 16px;
    margin: 0;
}

.user-settings-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #52B788;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.settings-alert {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    font-weight: 500;
}

.settings-alert-error {
    background-color: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.settings-alert-success {
    background-color: #c6f6d5;
    color: #2f855a;
    border: 1px solid #9ae6b4;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
}

.settings-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.settings-card-header {
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid #e2e8f0;
}

.settings-card-header h2 {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 8px 0;
}

.settings-card-header p {
    color: #718096;
    font-size: 14px;
    margin: 0;
}

.settings-card-content {
    padding: 24px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px 0;
    border-bottom: 1px solid #f7fafc;
}

.setting-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.setting-info {
    flex: 1;
    margin-right: 20px;
}

.setting-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.setting-icon {
    color: #52B788;
    font-size: 14px;
}

.setting-description {
    color: #718096;
    font-size: 14px;
    line-height: 1.5;
}

.setting-status {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 8px;
    font-size: 13px;
    font-weight: 500;
}

.status-icon {
    font-size: 12px;
}

.status-icon.success {
    color: #38a169;
}

.setting-control {
    flex-shrink: 0;
}

.toggle-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 24px;
    color: #cbd5e0;
    transition: color 0.2s ease;
    padding: 4px;
    border-radius: 4px;
}

.toggle-btn:hover {
    color: #a0aec0;
}

.toggle-btn.active {
    color: #52B788;
}

.toggle-btn.active:hover {
    color: #2C5F2D;
}

.security-tips {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.security-tip {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background-color: #f7fafc;
    border-radius: 8px;
    border-left: 4px solid #52B788;
}

.tip-icon {
    color: #52B788;
    font-size: 16px;
    margin-top: 2px;
    flex-shrink: 0;
}

.security-tip div {
    font-size: 14px;
    line-height: 1.5;
    color: #4a5568;
}

.security-tip strong {
    color: #2d3748;
}

/* Responsive Design */
@media (max-width: 768px) {
    .user-settings {
        padding: 16px;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .settings-card-header,
    .settings-card-content {
        padding: 16px;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .setting-info {
        margin-right: 0;
    }
    
    .setting-control {
        align-self: flex-end;
    }
    
    .settings-header h1 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .user-settings {
        padding: 12px;
    }
    
    .settings-header {
        margin-bottom: 20px;
    }
    
    .settings-card-header h2 {
        font-size: 18px;
    }
    
    .security-tip {
        padding: 12px;
    }
}
