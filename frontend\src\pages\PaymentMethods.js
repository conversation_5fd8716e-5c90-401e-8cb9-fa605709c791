import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaEye, FaEdit, FaTrash, FaTimes, FaPlus, FaSave, FaCreditCard, FaUniversity, FaBitcoin, FaMobile, FaPaypal } from 'react-icons/fa';
import './PaymentMethods.css';

const API_BASE_URL = '/backend';

function PaymentMethods() {
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [newMethod, setNewMethod] = useState({
    name: '',
    type: '',
    fields: [{ fieldName: '', fieldValue: '' }]
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [editingMethodId, setEditingMethodId] = useState(null);
  const [viewingMethod, setViewingMethod] = useState(null);
  const [showModal, setShowModal] = useState(false);

  const paymentTypes = [
    { value: 'bank', label: 'Bank Account' },
    { value: 'crypto', label: 'Cryptocurrency' },
    { value: 'mobile_money', label: 'Mobile Money' },
    { value: 'paypal', label: 'PayPal' },
    { value: 'other', label: 'Other' }
  ];

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    try {
      const response = await axios.get('payment_methods.php');
      console.log('Payment methods response:', response.data);

      // Check for either success flag or status code
      if (response.data.success || response.data.status === 200) {
        if (!response.data.data) {
          console.warn('No payment methods data in response:', response.data);
        }
        setPaymentMethods(response.data.data || []);
      } else {
        console.error('Failed response:', response.data);
        setError('Failed to fetch payment methods: ' + response.data.message);
      }
    } catch (err) {
      console.error('Error fetching payment methods:', err);
      setError('Failed to fetch payment methods. Please try again later.');
    }
  };

  // Handle input change for the payment method name and type
  const handleBasicInputChange = (e) => {
    const { name, value } = e.target;
    setNewMethod(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle changes to field names and values
  const handleFieldChange = (index, key, value) => {
    setNewMethod(prev => {
      const updatedFields = [...prev.fields];
      updatedFields[index] = {
        ...updatedFields[index],
        [key]: value
      };
      return {
        ...prev,
        fields: updatedFields
      };
    });
  };

  // Add a new field
  const addField = () => {
    setNewMethod(prev => ({
      ...prev,
      fields: [...prev.fields, { fieldName: '', fieldValue: '' }]
    }));
  };

  // Remove a field
  const removeField = (index) => {
    setNewMethod(prev => ({
      ...prev,
      fields: prev.fields.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!newMethod.name.trim()) {
      setError('Payment method name is required');
      return;
    }

    if (!newMethod.type) {
      setError('Payment method type is required');
      return;
    }

    // Validate fields
    const invalidFields = newMethod.fields.some(field =>
      !field.fieldName.trim() || !field.fieldValue.trim()
    );

    if (invalidFields) {
      setError('All fields must have both name and value');
      return;
    }

    try {
      const response = await axios.post(`${API_BASE_URL}/handlers/payment_methods.php`, newMethod);
      if (response.data.success) {
        setSuccess('Payment method added successfully!');
        fetchPaymentMethods();
        setNewMethod({
          name: '',
          type: '',
          fields: [{ fieldName: '', fieldValue: '' }]
        });
      } else {
        setError(response.data.message || 'Failed to add payment method');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to add payment method');
      console.error('Error details:', err.response?.data);
    }
  };

  const handleDelete = async (id) => {
    try {
      const response = await axios.delete(`${API_BASE_URL}/handlers/payment_methods.php?id=${id}`);
      if (response.data.success) {
        fetchPaymentMethods();
        setSuccess('Payment method deleted successfully!');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to delete payment method');
      console.error('Delete error:', err.response?.data);
    }
  };

  const handleEdit = (method) => {
    setEditingMethodId(method.id);
    setNewMethod({
      name: method.name,
      type: method.type,
      fields: Array.isArray(method.fields) && method.fields.length > 0
        ? method.fields
        : [{ fieldName: '', fieldValue: '' }]
    });
  };

  const handleView = (method) => {
    setViewingMethod(method);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setViewingMethod(null);
  };

  const handleUpdate = async (e) => {
    e.preventDefault();
    try {
      const response = await axios.put(
        `payment_methods.php?id=${editingMethodId}`,
        newMethod
      );
      if (response.data.success) {
        setSuccess('Payment method updated successfully!');
        fetchPaymentMethods();
        setEditingMethodId(null);
        setNewMethod({
          name: '',
          type: '',
          fields: [{ fieldName: '', fieldValue: '' }]
        });
      } else {
        setError(response.data.message || 'Failed to update payment method');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to update payment method');
    }
  };

  return (
    <div className="payment-methods-container">
      <div className="payment-methods-content">
        <div className="payment-methods-card">
          <h2>{editingMethodId ? 'Edit Payment Method' : 'Add New Payment Method'}</h2>
          {error && <div className="error-message">{error}</div>}
          {success && <div className="success-message">{success}</div>}

          <form onSubmit={editingMethodId ? handleUpdate : handleSubmit} className="payment-form">
            <div className="form-group">
              <label>Payment Method Name:</label>
              <input
                type="text"
                name="name"
                value={newMethod.name}
                onChange={handleBasicInputChange}
                placeholder="Enter payment method name"
                className="form-input"
                required
              />
            </div>

            <div className="form-group">
              <label>Payment Method Type:</label>
              <select
                name="type"
                value={newMethod.type}
                onChange={handleBasicInputChange}
                className="form-input"
                required
              >
                <option value="">Select a type</option>
                {paymentTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            {newMethod.fields.map((field, index) => (
              <div key={index} className="field-group">
                <div className="field-inputs">
                  <input
                    type="text"
                    value={field.fieldName}
                    onChange={(e) => handleFieldChange(index, 'fieldName', e.target.value)}
                    placeholder="Field Name (e.g., Email, Account Number)"
                    className="form-input"
                    required
                  />
                  <input
                    type="text"
                    value={field.fieldValue}
                    onChange={(e) => handleFieldChange(index, 'fieldValue', e.target.value)}
                    placeholder="Field Value"
                    className="form-input"
                    required
                  />
                </div>
                <button
                  type="button"
                  onClick={() => removeField(index)}
                  className="remove-field-button"
                  disabled={newMethod.fields.length === 1}
                >
                  <FaTrash style={{ marginRight: '8px' }} /> Remove Field
                </button>
              </div>
            ))}

            <button type="button" onClick={addField} className="add-field-button">
              <FaPlus style={{ marginRight: '8px' }} /> Add Field
            </button>

            <button type="submit" className="submit-button">
              <FaSave style={{ marginRight: '8px' }} /> {editingMethodId ? 'Update Payment Method' : 'Add Payment Method'}
            </button>
          </form>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900 flex items-center">
              <FaCreditCard className="mr-2 text-green-600" style={{ color: '#166534' }} />
              Payment Methods
            </h2>
            <span className="text-sm text-gray-500">
              {paymentMethods.length} method{paymentMethods.length !== 1 ? 's' : ''} configured
            </span>
          </div>

          {paymentMethods.length === 0 ? (
            <div className="text-center py-8">
              <FaCreditCard className="mx-auto text-gray-400 text-4xl mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No payment methods</h3>
              <p className="text-gray-500">Add your first payment method to get started.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {paymentMethods.map((method, index) => (
                <div key={method.id} className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-200 hover:border-green-300">
                  {/* Payment Method Icon */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                        {method.type === 'bank' ? (
                          <FaUniversity className="text-green-600 text-xl" />
                        ) : method.type === 'crypto' ? (
                          <FaBitcoin className="text-green-600 text-xl" />
                        ) : method.type === 'mobile_money' ? (
                          <FaMobile className="text-green-600 text-xl" />
                        ) : method.type === 'paypal' ? (
                          <FaPaypal className="text-green-600 text-xl" />
                        ) : (
                          <FaCreditCard className="text-green-600 text-xl" />
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 text-lg">{method.name}</h3>
                        <span className="text-sm text-gray-600 capitalize">
                          {paymentTypes.find(t => t.value === method.type)?.label || method.type}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                        Active
                      </span>
                    </div>
                  </div>

                  {/* Payment Method Details */}
                  <div className="mb-4">
                    <div className="text-sm text-gray-600 mb-2">
                      <span className="font-medium">Fields configured:</span> {Array.isArray(method.fields) ? method.fields.length : 0}
                    </div>
                    {Array.isArray(method.fields) && method.fields.length > 0 && (
                      <div className="space-y-1">
                        {method.fields.slice(0, 2).map((field, fieldIndex) => (
                          <div key={fieldIndex} className="flex justify-between text-xs">
                            <span className="text-gray-500 truncate">{field.fieldName}:</span>
                            <span className="text-gray-700 font-mono truncate ml-2">
                              {field.fieldValue.length > 15 ? `${field.fieldValue.substring(0, 15)}...` : field.fieldValue}
                            </span>
                          </div>
                        ))}
                        {method.fields.length > 2 && (
                          <div className="text-xs text-gray-500">
                            +{method.fields.length - 2} more field{method.fields.length - 2 !== 1 ? 's' : ''}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                    <button
                      onClick={() => handleView(method)}
                      className="flex items-center px-3 py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
                      title="View Details"
                    >
                      <FaEye className="mr-1" />
                      View
                    </button>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(method)}
                        className="flex items-center px-3 py-2 text-sm text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors"
                        title="Edit"
                      >
                        <FaEdit className="mr-1" />
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(method.id)}
                        className="flex items-center px-3 py-2 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                        title="Delete"
                      >
                        <FaTrash className="mr-1" />
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Modal for viewing payment method details */}
      {showModal && viewingMethod && (
        <div className="fixed inset-0 bg-black bg-opacity-60 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="bg-gradient-to-r from-green-600 to-emerald-600 text-white p-6 rounded-t-2xl">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-xl font-bold">{viewingMethod.name} Details</h3>
                  <p className="text-green-100 text-sm">
                    {paymentTypes.find(t => t.value === viewingMethod.type)?.label || viewingMethod.type}
                  </p>
                </div>
                <button
                  onClick={closeModal}
                  className="text-white hover:text-gray-200 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all"
                >
                  ×
                </button>
              </div>
            </div>
            <div className="p-6">
              <div className="mb-6">
                <div className="bg-gray-50 rounded-xl p-4 mb-4">
                  <h4 className="font-semibold text-gray-800 mb-3">Payment Method Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm text-gray-600">Type:</span>
                      <span className="ml-2 text-sm font-medium text-gray-900">
                        {paymentTypes.find(t => t.value === viewingMethod.type)?.label || viewingMethod.type}
                      </span>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Status:</span>
                      <span className="ml-2 inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                        Active
                      </span>
                    </div>
                  </div>
                </div>

                <h4 className="font-semibold text-gray-800 mb-3">Configuration Fields</h4>
                {Array.isArray(viewingMethod.fields) && viewingMethod.fields.length > 0 ? (
                  <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-green-600" style={{ backgroundColor: '#166534' }}>
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Field Name
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            Value
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {viewingMethod.fields.map((field, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {field.fieldName}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-mono">
                              {field.fieldValue}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-8 bg-gray-50 rounded-lg">
                    <p className="text-gray-500">No configuration fields available for this payment method.</p>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  onClick={() => { closeModal(); handleEdit(viewingMethod); }}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  Edit Method
                </button>
                <button
                  onClick={closeModal}
                  className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default PaymentMethods;