<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../includes/db_connect.php';
require_once '../vendor/autoload.php';

use PragmaRX\Google2FA\Google2FA;

try {
    $conn = getDBConnection();
    
    // Get user ID from query parameter
    $userId = $_GET['userId'] ?? null;
    
    if (!$userId) {
        throw new Exception("User ID is required");
    }
    
    // Verify user exists
    $stmt = $conn->prepare("SELECT user_id, username, email FROM users WHERE user_id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception("User not found");
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Generate new 2FA setup
        
        $google2fa = new Google2FA();
        
        // Generate secret key
        $secretKey = $google2fa->generateSecretKey();
        
        // Generate backup codes (10 codes by default)
        $backupCodesCount = 10;
        $backupCodes = [];
        for ($i = 0; $i < $backupCodesCount; $i++) {
            $backupCodes[] = strtoupper(substr(bin2hex(random_bytes(4)), 0, 8));
        }
        
        $conn->beginTransaction();
        
        // Store secret key and backup codes (not enabled yet)
        $stmt = $conn->prepare("
            INSERT INTO user_2fa (user_id, secret_key, auth_type, backup_codes, is_enabled, setup_completed) 
            VALUES (?, ?, 'google_auth', ?, 0, 0)
            ON DUPLICATE KEY UPDATE 
            secret_key = VALUES(secret_key), 
            backup_codes = VALUES(backup_codes), 
            is_enabled = 0,
            setup_completed = 0,
            updated_at = NOW()
        ");
        
        $backupCodesJson = json_encode($backupCodes);
        $stmt->execute([$userId, $secretKey, $backupCodesJson]);
        
        // Generate QR code URL
        $qrCodeUrl = $google2fa->getQRCodeUrl(
            'FanBet247',
            $user['email'],
            $secretKey
        );
        
        // Log 2FA setup initiation
        $stmt = $conn->prepare("
            INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, '2fa', '2fa_setup', ?, ?, ?)
        ");
        $stmt->execute([
            $userId,
            json_encode(['setup_initiated' => true, 'auth_type' => 'google_auth']),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'secret_key' => $secretKey,
            'qr_code_url' => $qrCodeUrl,
            'backup_codes' => $backupCodes,
            'manual_entry_key' => $secretKey,
            'issuer' => 'FanBet247',
            'account_name' => $user['email'],
            'setup_complete' => false,
            'message' => '2FA setup initiated. Please scan the QR code with Google Authenticator and verify with a test code.'
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Verify 2FA setup
        
        $input = json_decode(file_get_contents('php://input'), true);
        $verificationCode = $input['verification_code'] ?? '';
        
        if (empty($verificationCode) || strlen($verificationCode) !== 6) {
            throw new Exception("Please provide a valid 6-digit verification code");
        }
        
        // Get current 2FA setup
        $stmt = $conn->prepare("
            SELECT secret_key, backup_codes 
            FROM user_2fa 
            WHERE user_id = ? AND setup_completed = 0
        ");
        $stmt->execute([$userId]);
        $tfaSetup = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$tfaSetup) {
            throw new Exception("No pending 2FA setup found for this user");
        }
        
        // Verify the code
        $google2fa = new Google2FA();
        $isValid = $google2fa->verifyKey($tfaSetup['secret_key'], $verificationCode);
        
        if (!$isValid) {
            // Log failed verification attempt
            $stmt = $conn->prepare("
                INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
                VALUES (?, '2fa', '2fa_setup_failed', ?, ?, ?)
            ");
            $stmt->execute([
                $userId,
                json_encode(['reason' => 'invalid_verification_code']),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            
            throw new Exception("Invalid verification code. Please check your authenticator app and try again.");
        }
        
        $conn->beginTransaction();
        
        // Complete 2FA setup
        $stmt = $conn->prepare("
            UPDATE user_2fa 
            SET is_enabled = 1, setup_completed = 1, updated_at = NOW() 
            WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        
        // Update user's auth method
        $stmt = $conn->prepare("
            UPDATE users 
            SET tfa_enabled = 1, auth_method = 'password_2fa' 
            WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        
        // Log successful 2FA setup
        $stmt = $conn->prepare("
            INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
            VALUES (?, '2fa', '2fa_setup_complete', ?, ?, ?)
        ");
        $stmt->execute([
            $userId,
            json_encode(['setup_completed' => true, 'auth_type' => 'google_auth']),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => '2FA setup completed successfully! Your account is now protected with two-factor authentication.',
            'setup_complete' => true,
            'backup_codes' => json_decode($tfaSetup['backup_codes'], true)
        ]);
    }
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (Error $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error occurred'
    ]);
}
?>
