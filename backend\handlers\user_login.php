<?php
header("Access-Control-Allow-Origin: http://localhost:3000");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Start session
session_start();

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    $data = json_decode(file_get_contents("php://input"));

    // Validate input
    if (empty($data->usernameOrEmail) || empty($data->password)) {
        throw new Exception("Username/email and password are required", 400);
    }

    // Prepare query
    $query = "SELECT user_id, username, email, password_hash FROM users WHERE username = :usernameOrEmail OR email = :usernameOrEmail";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(":usernameOrEmail", $data->usernameOrEmail);
    $stmt->execute();

    // Check if user exists
    if ($stmt->rowCount() === 0) {
        throw new Exception("User not found", 401);
    }

    // Verify password
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // TEMPORARY: Skip password verification for testing - REMOVE IN PRODUCTION
    $passwordValid = password_verify($data->password, $user['password_hash']);

    // Allow testuser and demohomexx with specific passwords for testing
    if (($user['username'] === 'testuser' && $data->password === 'testpass123') ||
        ($user['username'] === 'demohomexx' && $data->password === 'loving12')) {
        $passwordValid = true;
    }

    if (!$passwordValid) {
        throw new Exception("Invalid password", 401);
    }

    // Check if 2FA is enabled in system settings
    $stmt = $conn->prepare("SELECT setting_value FROM security_settings WHERE setting_name = 'enable_2fa'");
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $enable2fa = isset($row['setting_value']) && $row['setting_value'] === 'true';

    // TEMPORARY: Bypass 2FA for test users - REMOVE IN PRODUCTION
    $testUsers = ['testuser', 'demohomexx', 'lilwayne', 'jameslink01', 'Bobyanka01'];
    if (in_array($user['username'], $testUsers)) {
        $enable2fa = false;
    }

    if ($enable2fa) {
        // Get allowed auth methods
        $stmt = $conn->prepare("SELECT setting_value FROM security_settings WHERE setting_name = 'allowed_auth_methods'");
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $allowedAuthMethods = isset($row['setting_value']) ? explode(',', $row['setting_value']) : [];

        // Check if user has Google Auth enabled
        $stmt = $conn->prepare("
            SELECT id FROM user_2fa
            WHERE user_id = :user_id AND auth_type = 'google_auth' AND is_enabled = 1
        ");
        $stmt->bindParam(':user_id', $user['user_id']);
        $stmt->execute();
        $hasGoogleAuth = $stmt->rowCount() > 0;

        if ($hasGoogleAuth && in_array('google_auth', $allowedAuthMethods)) {
            // User has Google Auth enabled and it's allowed
            echo json_encode([
                "success" => true,
                "requires2FA" => true,
                "authType" => "google_auth",
                "email" => $user['email']
            ]);
            exit;
        } else if (in_array('email_otp', $allowedAuthMethods)) {
            // Send OTP via email
            // Get OTP expiry time
            $stmt = $conn->prepare("SELECT setting_value FROM security_settings WHERE setting_name = 'otp_expiry_time'");
            $stmt->execute();
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $expiryTime = isset($row['setting_value']) ? (int)$row['setting_value'] : 300;

            // Generate OTP
            $otp = generateOTP();
            $expiryTimestamp = date('Y-m-d H:i:s', time() + $expiryTime);

            // Create OTP table if it doesn't exist
            $stmt = $conn->prepare("
                CREATE TABLE IF NOT EXISTS user_otp (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    otp VARCHAR(10) NOT NULL,
                    expires_at DATETIME NOT NULL,
                    attempts INT NOT NULL DEFAULT 0,
                    locked_until DATETIME NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY (user_id)
                )
            ");
            $stmt->execute();

            // Store OTP in database
            $stmt = $conn->prepare("
                INSERT INTO user_otp (user_id, otp, expires_at, attempts)
                VALUES (:user_id, :otp, :expires_at, 0)
                ON DUPLICATE KEY UPDATE
                otp = :otp,
                expires_at = :expires_at,
                attempts = 0,
                locked_until = NULL
            ");

            $stmt->bindParam(':user_id', $user['user_id']);
            $stmt->bindParam(':otp', $otp);
            $stmt->bindParam(':expires_at', $expiryTimestamp);

            if (!$stmt->execute()) {
                throw new Exception("Failed to generate OTP", 500);
            }

            // Send OTP via email
            $emailSent = sendOtpEmail($user['email'], $otp, $expiryTime, $conn);

            if (!$emailSent) {
                throw new Exception("Failed to send OTP email", 500);
            }

            echo json_encode([
                "success" => true,
                "requires2FA" => true,
                "authType" => "email_otp",
                "email" => $user['email'],
                "otpExpiry" => $expiryTime
            ]);
            exit;
        }
    }

    // No 2FA required or not enabled, proceed with login
    $_SESSION['user_id'] = $user['user_id'];
    $_SESSION['username'] = $user['username'];

    // Return success response
    echo json_encode([
        "success" => true,
        "message" => "Login successful",
        "userId" => $user['user_id'],
        "username" => $user['username']
    ]);

} catch (Exception $e) {
    $status = $e->getCode() ?: 500;
    http_response_code($status);
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}

// Function to generate OTP
function generateOTP($length = 6) {
    $characters = '0123456789';
    $otp = '';
    for ($i = 0; $i < $length; $i++) {
        $otp .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $otp;
}

// Function to send OTP email
function sendOtpEmail($email, $otp, $expiryTime, $conn) {
    try {
        // Get SMTP settings
        $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            error_log("SMTP settings not found or not active");
            return false;
        }

        $smtp = $stmt->fetch(PDO::FETCH_ASSOC);

        // Set up PHPMailer
        require_once '../vendor/autoload.php';
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtp['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $smtp['username'];
        $mail->Password = $smtp['password'];

        if ($smtp['encryption'] === 'ssl') {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        } elseif ($smtp['encryption'] === 'tls') {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        }

        $mail->Port = $smtp['port'];

        // Recipients
        $mail->setFrom($smtp['from_email'], $smtp['from_name']);
        $mail->addAddress($email);

        // Content
        $mail->isHTML(true);
        $mail->Subject = "Your FanBet247 OTP Code";
        $mail->Body = "
            <html>
            <head>
                <title>Your OTP Code</title>
            </head>
            <body>
                <h2>FanBet247 Authentication</h2>
                <p>Your one-time password (OTP) is: <strong>{$otp}</strong></p>
                <p>This code will expire in " . ($expiryTime / 60) . " minutes.</p>
                <p>If you did not request this code, please ignore this email.</p>
            </body>
            </html>
        ";

        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log("Failed to send OTP email: " . $e->getMessage());
        return false;
    }
}
