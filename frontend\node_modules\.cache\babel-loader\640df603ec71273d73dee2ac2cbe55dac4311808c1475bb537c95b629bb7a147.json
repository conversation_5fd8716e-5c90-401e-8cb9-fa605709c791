{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaShieldAlt, FaKey, FaEnvelope, FaUser, FaBell, FaToggleOn, FaToggleOff, FaQrcode, FaCopy, FaCheck, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';\nimport UserLayout from '../components/UserLayout';\nimport User2FASetup from '../components/User/User2FASetup';\nimport './UserSettings.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\nfunction UserSettings() {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // User data\n  const [userId, setUserId] = useState(null);\n  const [userEmail, setUserEmail] = useState('');\n\n  // Security settings\n  const [securitySettings, setSecuritySettings] = useState({\n    otp_enabled: false,\n    tfa_enabled: false,\n    auth_method: 'password_only'\n  });\n\n  // 2FA setup state\n  const [tfaSetup, setTfaSetup] = useState(null);\n  const [showTfaSetup, setShowTfaSetup] = useState(false);\n\n  // Account settings\n  const [accountSettings, setAccountSettings] = useState({\n    email_notifications: true,\n    security_alerts: true,\n    login_notifications: true\n  });\n  useEffect(() => {\n    const storedUserId = localStorage.getItem('userId');\n    if (!storedUserId) {\n      navigate('/user/login');\n      return;\n    }\n    setUserId(storedUserId);\n    loadUserSettings();\n  }, [navigate]);\n  const loadUserSettings = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const userId = localStorage.getItem('userId');\n\n      // Load user security settings\n      const securityResponse = await axios.get(`${API_BASE_URL}/handlers/user_security_settings.php?userId=${userId}`);\n      if (securityResponse.data.success) {\n        setSecuritySettings(securityResponse.data.settings);\n        setUserEmail(securityResponse.data.user_email);\n        setTfaSetup(securityResponse.data.tfa_setup);\n      }\n\n      // Load account preferences\n      const accountResponse = await axios.get(`${API_BASE_URL}/handlers/user_account_settings.php?userId=${userId}`);\n      if (accountResponse.data.success) {\n        setAccountSettings(accountResponse.data.settings);\n      }\n    } catch (err) {\n      console.error('Settings load error:', err);\n      setError('Failed to load settings. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleOtpToggle = async () => {\n    try {\n      setError('');\n      const newOtpState = !securitySettings.otp_enabled;\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_toggle_otp.php`, {\n        userId: userId,\n        enabled: newOtpState\n      });\n      if (response.data.success) {\n        setSecuritySettings(prev => ({\n          ...prev,\n          otp_enabled: newOtpState\n        }));\n        setSuccess(`OTP ${newOtpState ? 'enabled' : 'disabled'} successfully`);\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update OTP setting');\n      }\n    } catch (err) {\n      setError('Failed to update OTP setting. Please try again.');\n    }\n  };\n  const handle2FAToggle = async () => {\n    try {\n      setError('');\n      if (!securitySettings.tfa_enabled) {\n        // Enable 2FA - show setup\n        setShowTfaSetup(true);\n      } else {\n        // Disable 2FA\n        const response = await axios.post(`${API_BASE_URL}/handlers/user_disable_2fa.php`, {\n          userId: userId\n        });\n        if (response.data.success) {\n          setSecuritySettings(prev => ({\n            ...prev,\n            tfa_enabled: false\n          }));\n          setTfaSetup(null);\n          setSuccess('2FA disabled successfully');\n          setTimeout(() => setSuccess(''), 3000);\n        } else {\n          setError(response.data.message || 'Failed to disable 2FA');\n        }\n      }\n    } catch (err) {\n      setError('Failed to update 2FA setting. Please try again.');\n    }\n  };\n  const handleAccountSettingToggle = async settingName => {\n    try {\n      setError('');\n      const newValue = !accountSettings[settingName];\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_account_settings.php`, {\n        userId: userId,\n        setting: settingName,\n        value: newValue\n      });\n      if (response.data.success) {\n        setAccountSettings(prev => ({\n          ...prev,\n          [settingName]: newValue\n        }));\n        setSuccess('Setting updated successfully');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update setting');\n      }\n    } catch (err) {\n      setError('Failed to update setting. Please try again.');\n    }\n  };\n  const on2FASetupComplete = () => {\n    setShowTfaSetup(false);\n    setSecuritySettings(prev => ({\n      ...prev,\n      tfa_enabled: true\n    }));\n    setSuccess('2FA setup completed successfully!');\n    setTimeout(() => setSuccess(''), 3000);\n    loadUserSettings(); // Reload to get updated setup info\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(UserLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-settings-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 13\n    }, this);\n  }\n  if (showTfaSetup) {\n    return /*#__PURE__*/_jsxDEV(UserLayout, {\n      children: /*#__PURE__*/_jsxDEV(User2FASetup, {\n        userId: userId,\n        userEmail: userEmail,\n        onSuccess: on2FASetupComplete,\n        onBack: () => setShowTfaSetup(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(UserLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-settings\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: [/*#__PURE__*/_jsxDEV(FaUser, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 25\n          }, this), \" Account Settings\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Manage your account security and preferences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 17\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-alert settings-alert-error\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 21\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-alert settings-alert-success\",\n        children: [/*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-card-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 33\n              }, this), \" Security Settings\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Configure two-factor authentication and security options\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"setting-title\",\n                  children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                    className: \"setting-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 41\n                  }, this), \"One-Time Password (OTP)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"setting-description\",\n                  children: \"Receive a verification code via email when logging in\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-control\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `toggle-btn ${securitySettings.otp_enabled ? 'active' : ''}`,\n                  onClick: handleOtpToggle,\n                  children: securitySettings.otp_enabled ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 73\n                  }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 90\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"setting-title\",\n                  children: [/*#__PURE__*/_jsxDEV(FaQrcode, {\n                    className: \"setting-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 41\n                  }, this), \"Two-Factor Authentication (2FA)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"setting-description\",\n                  children: \"Use Google Authenticator or similar app for enhanced security\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 37\n                }, this), (tfaSetup === null || tfaSetup === void 0 ? void 0 : tfaSetup.setup_completed) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"setting-status\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                    className: \"status-icon success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 45\n                  }, this), \"2FA is configured and active\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-control\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `toggle-btn ${securitySettings.tfa_enabled ? 'active' : ''}`,\n                  onClick: handle2FAToggle,\n                  children: securitySettings.tfa_enabled ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 73\n                  }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 90\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-card-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: [/*#__PURE__*/_jsxDEV(FaBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 33\n              }, this), \" Notification Preferences\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Choose what notifications you want to receive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"setting-title\",\n                  children: \"Email Notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"setting-description\",\n                  children: \"Receive general notifications via email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-control\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `toggle-btn ${accountSettings.email_notifications ? 'active' : ''}`,\n                  onClick: () => handleAccountSettingToggle('email_notifications'),\n                  children: accountSettings.email_notifications ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 80\n                  }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 97\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"setting-title\",\n                  children: \"Security Alerts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"setting-description\",\n                  children: \"Get notified about security-related activities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-control\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `toggle-btn ${accountSettings.security_alerts ? 'active' : ''}`,\n                  onClick: () => handleAccountSettingToggle('security_alerts'),\n                  children: accountSettings.security_alerts ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 76\n                  }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 93\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"setting-title\",\n                  children: \"Login Notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"setting-description\",\n                  children: \"Get notified when someone logs into your account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"setting-control\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `toggle-btn ${accountSettings.login_notifications ? 'active' : ''}`,\n                  onClick: () => handleAccountSettingToggle('login_notifications'),\n                  children: accountSettings.login_notifications ? /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 80\n                  }, this) : /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 97\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-card-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 33\n              }, this), \" Security Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Important security recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-card-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"security-tips\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"security-tip\",\n                children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {\n                  className: \"tip-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Enable 2FA:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 41\n                  }, this), \" Two-factor authentication provides an extra layer of security for your account.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"security-tip\",\n                children: [/*#__PURE__*/_jsxDEV(FaEnvelope, {\n                  className: \"tip-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"OTP Verification:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 41\n                  }, this), \" Email OTP helps verify your identity during login attempts.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"security-tip\",\n                children: [/*#__PURE__*/_jsxDEV(FaKey, {\n                  className: \"tip-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Strong Password:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 41\n                  }, this), \" Use a unique, strong password for your FanBet247 account.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 222,\n    columnNumber: 9\n  }, this);\n}\n_s(UserSettings, \"rSI+10uWANC1B1ADaSLui1ZAIA0=\", false, function () {\n  return [useNavigate];\n});\n_c = UserSettings;\nexport default UserSettings;\nvar _c;\n$RefreshReg$(_c, \"UserSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "axios", "FaShieldAlt", "FaKey", "FaEnvelope", "FaUser", "FaBell", "FaToggleOn", "FaToggleOff", "FaQrcode", "FaCopy", "FaCheck", "FaExclamationTriangle", "FaInfoCircle", "UserLayout", "User2FASetup", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "UserSettings", "_s", "navigate", "loading", "setLoading", "error", "setError", "success", "setSuccess", "userId", "setUserId", "userEmail", "setUserEmail", "securitySettings", "setSecuritySettings", "otp_enabled", "tfa_enabled", "auth_method", "tfaSetup", "setTfaSetup", "showTfaSetup", "setShowTfaSetup", "accountSettings", "setAccountSettings", "email_notifications", "security_alerts", "login_notifications", "storedUserId", "localStorage", "getItem", "loadUserSettings", "securityResponse", "get", "data", "settings", "user_email", "tfa_setup", "accountResponse", "err", "console", "handleOtpToggle", "newOtpState", "response", "post", "enabled", "prev", "setTimeout", "message", "handle2FAToggle", "handleAccountSettingToggle", "<PERSON><PERSON><PERSON>", "newValue", "setting", "value", "on2FASetupComplete", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSuccess", "onBack", "onClick", "setup_completed", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserSettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { \n    FaShieldAlt, \n    <PERSON><PERSON><PERSON><PERSON>, \n    <PERSON><PERSON><PERSON>n<PERSON>ope, \n    <PERSON><PERSON><PERSON><PERSON>, \n    <PERSON><PERSON><PERSON>ell, \n    FaToggleOn, \n    FaToggleOff,\n    FaQrcode,\n    FaCopy,\n    FaCheck,\n    FaExclamationTriangle,\n    FaInfoCircle\n} from 'react-icons/fa';\nimport UserLayout from '../components/UserLayout';\nimport User2FASetup from '../components/User/User2FASetup';\nimport './UserSettings.css';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\n\nfunction UserSettings() {\n    const navigate = useNavigate();\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    \n    // User data\n    const [userId, setUserId] = useState(null);\n    const [userEmail, setUserEmail] = useState('');\n    \n    // Security settings\n    const [securitySettings, setSecuritySettings] = useState({\n        otp_enabled: false,\n        tfa_enabled: false,\n        auth_method: 'password_only'\n    });\n    \n    // 2FA setup state\n    const [tfaSetup, setTfaSetup] = useState(null);\n    const [showTfaSetup, setShowTfaSetup] = useState(false);\n    \n    // Account settings\n    const [accountSettings, setAccountSettings] = useState({\n        email_notifications: true,\n        security_alerts: true,\n        login_notifications: true\n    });\n\n    useEffect(() => {\n        const storedUserId = localStorage.getItem('userId');\n        if (!storedUserId) {\n            navigate('/user/login');\n            return;\n        }\n        \n        setUserId(storedUserId);\n        loadUserSettings();\n    }, [navigate]);\n\n    const loadUserSettings = async () => {\n        try {\n            setLoading(true);\n            setError('');\n\n            const userId = localStorage.getItem('userId');\n            \n            // Load user security settings\n            const securityResponse = await axios.get(\n                `${API_BASE_URL}/handlers/user_security_settings.php?userId=${userId}`\n            );\n\n            if (securityResponse.data.success) {\n                setSecuritySettings(securityResponse.data.settings);\n                setUserEmail(securityResponse.data.user_email);\n                setTfaSetup(securityResponse.data.tfa_setup);\n            }\n\n            // Load account preferences\n            const accountResponse = await axios.get(\n                `${API_BASE_URL}/handlers/user_account_settings.php?userId=${userId}`\n            );\n\n            if (accountResponse.data.success) {\n                setAccountSettings(accountResponse.data.settings);\n            }\n\n        } catch (err) {\n            console.error('Settings load error:', err);\n            setError('Failed to load settings. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleOtpToggle = async () => {\n        try {\n            setError('');\n            const newOtpState = !securitySettings.otp_enabled;\n            \n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/user_toggle_otp.php`,\n                {\n                    userId: userId,\n                    enabled: newOtpState\n                }\n            );\n\n            if (response.data.success) {\n                setSecuritySettings(prev => ({\n                    ...prev,\n                    otp_enabled: newOtpState\n                }));\n                setSuccess(`OTP ${newOtpState ? 'enabled' : 'disabled'} successfully`);\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to update OTP setting');\n            }\n        } catch (err) {\n            setError('Failed to update OTP setting. Please try again.');\n        }\n    };\n\n    const handle2FAToggle = async () => {\n        try {\n            setError('');\n            \n            if (!securitySettings.tfa_enabled) {\n                // Enable 2FA - show setup\n                setShowTfaSetup(true);\n            } else {\n                // Disable 2FA\n                const response = await axios.post(\n                    `${API_BASE_URL}/handlers/user_disable_2fa.php`,\n                    { userId: userId }\n                );\n\n                if (response.data.success) {\n                    setSecuritySettings(prev => ({\n                        ...prev,\n                        tfa_enabled: false\n                    }));\n                    setTfaSetup(null);\n                    setSuccess('2FA disabled successfully');\n                    setTimeout(() => setSuccess(''), 3000);\n                } else {\n                    setError(response.data.message || 'Failed to disable 2FA');\n                }\n            }\n        } catch (err) {\n            setError('Failed to update 2FA setting. Please try again.');\n        }\n    };\n\n    const handleAccountSettingToggle = async (settingName) => {\n        try {\n            setError('');\n            const newValue = !accountSettings[settingName];\n            \n            const response = await axios.post(\n                `${API_BASE_URL}/handlers/user_account_settings.php`,\n                {\n                    userId: userId,\n                    setting: settingName,\n                    value: newValue\n                }\n            );\n\n            if (response.data.success) {\n                setAccountSettings(prev => ({\n                    ...prev,\n                    [settingName]: newValue\n                }));\n                setSuccess('Setting updated successfully');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to update setting');\n            }\n        } catch (err) {\n            setError('Failed to update setting. Please try again.');\n        }\n    };\n\n    const on2FASetupComplete = () => {\n        setShowTfaSetup(false);\n        setSecuritySettings(prev => ({\n            ...prev,\n            tfa_enabled: true\n        }));\n        setSuccess('2FA setup completed successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n        loadUserSettings(); // Reload to get updated setup info\n    };\n\n    if (loading) {\n        return (\n            <UserLayout>\n                <div className=\"user-settings-loading\">\n                    <div className=\"loading-spinner\"></div>\n                    <p>Loading settings...</p>\n                </div>\n            </UserLayout>\n        );\n    }\n\n    if (showTfaSetup) {\n        return (\n            <UserLayout>\n                <User2FASetup\n                    userId={userId}\n                    userEmail={userEmail}\n                    onSuccess={on2FASetupComplete}\n                    onBack={() => setShowTfaSetup(false)}\n                />\n            </UserLayout>\n        );\n    }\n\n    return (\n        <UserLayout>\n            <div className=\"user-settings\">\n                <div className=\"settings-header\">\n                    <h1><FaUser /> Account Settings</h1>\n                    <p>Manage your account security and preferences</p>\n                </div>\n\n                {error && (\n                    <div className=\"settings-alert settings-alert-error\">\n                        <FaExclamationTriangle />\n                        <span>{error}</span>\n                    </div>\n                )}\n\n                {success && (\n                    <div className=\"settings-alert settings-alert-success\">\n                        <FaCheck />\n                        <span>{success}</span>\n                    </div>\n                )}\n\n                <div className=\"settings-grid\">\n                    {/* Security Settings */}\n                    <div className=\"settings-card\">\n                        <div className=\"settings-card-header\">\n                            <h2><FaShieldAlt /> Security Settings</h2>\n                            <p>Configure two-factor authentication and security options</p>\n                        </div>\n\n                        <div className=\"settings-card-content\">\n                            {/* OTP Setting */}\n                            <div className=\"setting-item\">\n                                <div className=\"setting-info\">\n                                    <div className=\"setting-title\">\n                                        <FaEnvelope className=\"setting-icon\" />\n                                        One-Time Password (OTP)\n                                    </div>\n                                    <div className=\"setting-description\">\n                                        Receive a verification code via email when logging in\n                                    </div>\n                                </div>\n                                <div className=\"setting-control\">\n                                    <button\n                                        className={`toggle-btn ${securitySettings.otp_enabled ? 'active' : ''}`}\n                                        onClick={handleOtpToggle}\n                                    >\n                                        {securitySettings.otp_enabled ? <FaToggleOn /> : <FaToggleOff />}\n                                    </button>\n                                </div>\n                            </div>\n\n                            {/* 2FA Setting */}\n                            <div className=\"setting-item\">\n                                <div className=\"setting-info\">\n                                    <div className=\"setting-title\">\n                                        <FaQrcode className=\"setting-icon\" />\n                                        Two-Factor Authentication (2FA)\n                                    </div>\n                                    <div className=\"setting-description\">\n                                        Use Google Authenticator or similar app for enhanced security\n                                    </div>\n                                    {tfaSetup?.setup_completed && (\n                                        <div className=\"setting-status\">\n                                            <FaCheck className=\"status-icon success\" />\n                                            2FA is configured and active\n                                        </div>\n                                    )}\n                                </div>\n                                <div className=\"setting-control\">\n                                    <button\n                                        className={`toggle-btn ${securitySettings.tfa_enabled ? 'active' : ''}`}\n                                        onClick={handle2FAToggle}\n                                    >\n                                        {securitySettings.tfa_enabled ? <FaToggleOn /> : <FaToggleOff />}\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Account Preferences */}\n                    <div className=\"settings-card\">\n                        <div className=\"settings-card-header\">\n                            <h2><FaBell /> Notification Preferences</h2>\n                            <p>Choose what notifications you want to receive</p>\n                        </div>\n\n                        <div className=\"settings-card-content\">\n                            <div className=\"setting-item\">\n                                <div className=\"setting-info\">\n                                    <div className=\"setting-title\">Email Notifications</div>\n                                    <div className=\"setting-description\">\n                                        Receive general notifications via email\n                                    </div>\n                                </div>\n                                <div className=\"setting-control\">\n                                    <button\n                                        className={`toggle-btn ${accountSettings.email_notifications ? 'active' : ''}`}\n                                        onClick={() => handleAccountSettingToggle('email_notifications')}\n                                    >\n                                        {accountSettings.email_notifications ? <FaToggleOn /> : <FaToggleOff />}\n                                    </button>\n                                </div>\n                            </div>\n\n                            <div className=\"setting-item\">\n                                <div className=\"setting-info\">\n                                    <div className=\"setting-title\">Security Alerts</div>\n                                    <div className=\"setting-description\">\n                                        Get notified about security-related activities\n                                    </div>\n                                </div>\n                                <div className=\"setting-control\">\n                                    <button\n                                        className={`toggle-btn ${accountSettings.security_alerts ? 'active' : ''}`}\n                                        onClick={() => handleAccountSettingToggle('security_alerts')}\n                                    >\n                                        {accountSettings.security_alerts ? <FaToggleOn /> : <FaToggleOff />}\n                                    </button>\n                                </div>\n                            </div>\n\n                            <div className=\"setting-item\">\n                                <div className=\"setting-info\">\n                                    <div className=\"setting-title\">Login Notifications</div>\n                                    <div className=\"setting-description\">\n                                        Get notified when someone logs into your account\n                                    </div>\n                                </div>\n                                <div className=\"setting-control\">\n                                    <button\n                                        className={`toggle-btn ${accountSettings.login_notifications ? 'active' : ''}`}\n                                        onClick={() => handleAccountSettingToggle('login_notifications')}\n                                    >\n                                        {accountSettings.login_notifications ? <FaToggleOn /> : <FaToggleOff />}\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                    {/* Security Info */}\n                    <div className=\"settings-card\">\n                        <div className=\"settings-card-header\">\n                            <h2><FaInfoCircle /> Security Information</h2>\n                            <p>Important security recommendations</p>\n                        </div>\n\n                        <div className=\"settings-card-content\">\n                            <div className=\"security-tips\">\n                                <div className=\"security-tip\">\n                                    <FaShieldAlt className=\"tip-icon\" />\n                                    <div>\n                                        <strong>Enable 2FA:</strong> Two-factor authentication provides an extra layer of security for your account.\n                                    </div>\n                                </div>\n                                <div className=\"security-tip\">\n                                    <FaEnvelope className=\"tip-icon\" />\n                                    <div>\n                                        <strong>OTP Verification:</strong> Email OTP helps verify your identity during login attempts.\n                                    </div>\n                                </div>\n                                <div className=\"security-tip\">\n                                    <FaKey className=\"tip-icon\" />\n                                    <div>\n                                        <strong>Strong Password:</strong> Use a unique, strong password for your FanBet247 account.\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </UserLayout>\n    );\n}\n\nexport default UserSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACIC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,qBAAqB,EACrBC,YAAY,QACT,gBAAgB;AACvB,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,oCAAoC;AAE/F,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACiC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC;IACrDuC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC;IACnDgD,mBAAmB,EAAE,IAAI;IACzBC,eAAe,EAAE,IAAI;IACrBC,mBAAmB,EAAE;EACzB,CAAC,CAAC;EAEFjD,SAAS,CAAC,MAAM;IACZ,MAAMkD,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACnD,IAAI,CAACF,YAAY,EAAE;MACfzB,QAAQ,CAAC,aAAa,CAAC;MACvB;IACJ;IAEAQ,SAAS,CAACiB,YAAY,CAAC;IACvBG,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC5B,QAAQ,CAAC,CAAC;EAEd,MAAM4B,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACA1B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMG,MAAM,GAAGmB,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;;MAE7C;MACA,MAAME,gBAAgB,GAAG,MAAMpD,KAAK,CAACqD,GAAG,CACpC,GAAGpC,YAAY,+CAA+Ca,MAAM,EACxE,CAAC;MAED,IAAIsB,gBAAgB,CAACE,IAAI,CAAC1B,OAAO,EAAE;QAC/BO,mBAAmB,CAACiB,gBAAgB,CAACE,IAAI,CAACC,QAAQ,CAAC;QACnDtB,YAAY,CAACmB,gBAAgB,CAACE,IAAI,CAACE,UAAU,CAAC;QAC9ChB,WAAW,CAACY,gBAAgB,CAACE,IAAI,CAACG,SAAS,CAAC;MAChD;;MAEA;MACA,MAAMC,eAAe,GAAG,MAAM1D,KAAK,CAACqD,GAAG,CACnC,GAAGpC,YAAY,8CAA8Ca,MAAM,EACvE,CAAC;MAED,IAAI4B,eAAe,CAACJ,IAAI,CAAC1B,OAAO,EAAE;QAC9BgB,kBAAkB,CAACc,eAAe,CAACJ,IAAI,CAACC,QAAQ,CAAC;MACrD;IAEJ,CAAC,CAAC,OAAOI,GAAG,EAAE;MACVC,OAAO,CAAClC,KAAK,CAAC,sBAAsB,EAAEiC,GAAG,CAAC;MAC1ChC,QAAQ,CAAC,4CAA4C,CAAC;IAC1D,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMoC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACAlC,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMmC,WAAW,GAAG,CAAC5B,gBAAgB,CAACE,WAAW;MAEjD,MAAM2B,QAAQ,GAAG,MAAM/D,KAAK,CAACgE,IAAI,CAC7B,GAAG/C,YAAY,+BAA+B,EAC9C;QACIa,MAAM,EAAEA,MAAM;QACdmC,OAAO,EAAEH;MACb,CACJ,CAAC;MAED,IAAIC,QAAQ,CAACT,IAAI,CAAC1B,OAAO,EAAE;QACvBO,mBAAmB,CAAC+B,IAAI,KAAK;UACzB,GAAGA,IAAI;UACP9B,WAAW,EAAE0B;QACjB,CAAC,CAAC,CAAC;QACHjC,UAAU,CAAC,OAAOiC,WAAW,GAAG,SAAS,GAAG,UAAU,eAAe,CAAC;QACtEK,UAAU,CAAC,MAAMtC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACoC,QAAQ,CAACT,IAAI,CAACc,OAAO,IAAI,8BAA8B,CAAC;MACrE;IACJ,CAAC,CAAC,OAAOT,GAAG,EAAE;MACVhC,QAAQ,CAAC,iDAAiD,CAAC;IAC/D;EACJ,CAAC;EAED,MAAM0C,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACA1C,QAAQ,CAAC,EAAE,CAAC;MAEZ,IAAI,CAACO,gBAAgB,CAACG,WAAW,EAAE;QAC/B;QACAK,eAAe,CAAC,IAAI,CAAC;MACzB,CAAC,MAAM;QACH;QACA,MAAMqB,QAAQ,GAAG,MAAM/D,KAAK,CAACgE,IAAI,CAC7B,GAAG/C,YAAY,gCAAgC,EAC/C;UAAEa,MAAM,EAAEA;QAAO,CACrB,CAAC;QAED,IAAIiC,QAAQ,CAACT,IAAI,CAAC1B,OAAO,EAAE;UACvBO,mBAAmB,CAAC+B,IAAI,KAAK;YACzB,GAAGA,IAAI;YACP7B,WAAW,EAAE;UACjB,CAAC,CAAC,CAAC;UACHG,WAAW,CAAC,IAAI,CAAC;UACjBX,UAAU,CAAC,2BAA2B,CAAC;UACvCsC,UAAU,CAAC,MAAMtC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;QAC1C,CAAC,MAAM;UACHF,QAAQ,CAACoC,QAAQ,CAACT,IAAI,CAACc,OAAO,IAAI,uBAAuB,CAAC;QAC9D;MACJ;IACJ,CAAC,CAAC,OAAOT,GAAG,EAAE;MACVhC,QAAQ,CAAC,iDAAiD,CAAC;IAC/D;EACJ,CAAC;EAED,MAAM2C,0BAA0B,GAAG,MAAOC,WAAW,IAAK;IACtD,IAAI;MACA5C,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAM6C,QAAQ,GAAG,CAAC7B,eAAe,CAAC4B,WAAW,CAAC;MAE9C,MAAMR,QAAQ,GAAG,MAAM/D,KAAK,CAACgE,IAAI,CAC7B,GAAG/C,YAAY,qCAAqC,EACpD;QACIa,MAAM,EAAEA,MAAM;QACd2C,OAAO,EAAEF,WAAW;QACpBG,KAAK,EAAEF;MACX,CACJ,CAAC;MAED,IAAIT,QAAQ,CAACT,IAAI,CAAC1B,OAAO,EAAE;QACvBgB,kBAAkB,CAACsB,IAAI,KAAK;UACxB,GAAGA,IAAI;UACP,CAACK,WAAW,GAAGC;QACnB,CAAC,CAAC,CAAC;QACH3C,UAAU,CAAC,8BAA8B,CAAC;QAC1CsC,UAAU,CAAC,MAAMtC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACoC,QAAQ,CAACT,IAAI,CAACc,OAAO,IAAI,0BAA0B,CAAC;MACjE;IACJ,CAAC,CAAC,OAAOT,GAAG,EAAE;MACVhC,QAAQ,CAAC,6CAA6C,CAAC;IAC3D;EACJ,CAAC;EAED,MAAMgD,kBAAkB,GAAGA,CAAA,KAAM;IAC7BjC,eAAe,CAAC,KAAK,CAAC;IACtBP,mBAAmB,CAAC+B,IAAI,KAAK;MACzB,GAAGA,IAAI;MACP7B,WAAW,EAAE;IACjB,CAAC,CAAC,CAAC;IACHR,UAAU,CAAC,mCAAmC,CAAC;IAC/CsC,UAAU,CAAC,MAAMtC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACtCsB,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;EAED,IAAI3B,OAAO,EAAE;IACT,oBACIR,OAAA,CAACH,UAAU;MAAA+D,QAAA,eACP5D,OAAA;QAAK6D,SAAS,EAAC,uBAAuB;QAAAD,QAAA,gBAClC5D,OAAA;UAAK6D,SAAS,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCjE,OAAA;UAAA4D,QAAA,EAAG;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAErB;EAEA,IAAIxC,YAAY,EAAE;IACd,oBACIzB,OAAA,CAACH,UAAU;MAAA+D,QAAA,eACP5D,OAAA,CAACF,YAAY;QACTgB,MAAM,EAAEA,MAAO;QACfE,SAAS,EAAEA,SAAU;QACrBkD,SAAS,EAAEP,kBAAmB;QAC9BQ,MAAM,EAAEA,CAAA,KAAMzC,eAAe,CAAC,KAAK;MAAE;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAErB;EAEA,oBACIjE,OAAA,CAACH,UAAU;IAAA+D,QAAA,eACP5D,OAAA;MAAK6D,SAAS,EAAC,eAAe;MAAAD,QAAA,gBAC1B5D,OAAA;QAAK6D,SAAS,EAAC,iBAAiB;QAAAD,QAAA,gBAC5B5D,OAAA;UAAA4D,QAAA,gBAAI5D,OAAA,CAACZ,MAAM;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAAiB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpCjE,OAAA;UAAA4D,QAAA,EAAG;QAA4C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,EAELvD,KAAK,iBACFV,OAAA;QAAK6D,SAAS,EAAC,qCAAqC;QAAAD,QAAA,gBAChD5D,OAAA,CAACL,qBAAqB;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzBjE,OAAA;UAAA4D,QAAA,EAAOlD;QAAK;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACR,EAEArD,OAAO,iBACJZ,OAAA;QAAK6D,SAAS,EAAC,uCAAuC;QAAAD,QAAA,gBAClD5D,OAAA,CAACN,OAAO;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACXjE,OAAA;UAAA4D,QAAA,EAAOhD;QAAO;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CACR,eAEDjE,OAAA;QAAK6D,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAE1B5D,OAAA;UAAK6D,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAC1B5D,OAAA;YAAK6D,SAAS,EAAC,sBAAsB;YAAAD,QAAA,gBACjC5D,OAAA;cAAA4D,QAAA,gBAAI5D,OAAA,CAACf,WAAW;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAAkB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1CjE,OAAA;cAAA4D,QAAA,EAAG;YAAwD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAENjE,OAAA;YAAK6D,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBAElC5D,OAAA;cAAK6D,SAAS,EAAC,cAAc;cAAAD,QAAA,gBACzB5D,OAAA;gBAAK6D,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzB5D,OAAA;kBAAK6D,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC1B5D,OAAA,CAACb,UAAU;oBAAC0E,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,2BAE3C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNjE,OAAA;kBAAK6D,SAAS,EAAC,qBAAqB;kBAAAD,QAAA,EAAC;gBAErC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNjE,OAAA;gBAAK6D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC5B5D,OAAA;kBACI6D,SAAS,EAAE,cAAc3C,gBAAgB,CAACE,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;kBACxEgD,OAAO,EAAEvB,eAAgB;kBAAAe,QAAA,EAExB1C,gBAAgB,CAACE,WAAW,gBAAGpB,OAAA,CAACV,UAAU;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACT,WAAW;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAGNjE,OAAA;cAAK6D,SAAS,EAAC,cAAc;cAAAD,QAAA,gBACzB5D,OAAA;gBAAK6D,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzB5D,OAAA;kBAAK6D,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC1B5D,OAAA,CAACR,QAAQ;oBAACqE,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mCAEzC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNjE,OAAA;kBAAK6D,SAAS,EAAC,qBAAqB;kBAAAD,QAAA,EAAC;gBAErC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EACL,CAAA1C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE8C,eAAe,kBACtBrE,OAAA;kBAAK6D,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,gBAC3B5D,OAAA,CAACN,OAAO;oBAACmE,SAAS,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gCAE/C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACNjE,OAAA;gBAAK6D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC5B5D,OAAA;kBACI6D,SAAS,EAAE,cAAc3C,gBAAgB,CAACG,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;kBACxE+C,OAAO,EAAEf,eAAgB;kBAAAO,QAAA,EAExB1C,gBAAgB,CAACG,WAAW,gBAAGrB,OAAA,CAACV,UAAU;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACT,WAAW;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNjE,OAAA;UAAK6D,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAC1B5D,OAAA;YAAK6D,SAAS,EAAC,sBAAsB;YAAAD,QAAA,gBACjC5D,OAAA;cAAA4D,QAAA,gBAAI5D,OAAA,CAACX,MAAM;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,6BAAyB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CjE,OAAA;cAAA4D,QAAA,EAAG;YAA6C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAENjE,OAAA;YAAK6D,SAAS,EAAC,uBAAuB;YAAAD,QAAA,gBAClC5D,OAAA;cAAK6D,SAAS,EAAC,cAAc;cAAAD,QAAA,gBACzB5D,OAAA;gBAAK6D,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzB5D,OAAA;kBAAK6D,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDjE,OAAA;kBAAK6D,SAAS,EAAC,qBAAqB;kBAAAD,QAAA,EAAC;gBAErC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNjE,OAAA;gBAAK6D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC5B5D,OAAA;kBACI6D,SAAS,EAAE,cAAclC,eAAe,CAACE,mBAAmB,GAAG,QAAQ,GAAG,EAAE,EAAG;kBAC/EuC,OAAO,EAAEA,CAAA,KAAMd,0BAA0B,CAAC,qBAAqB,CAAE;kBAAAM,QAAA,EAEhEjC,eAAe,CAACE,mBAAmB,gBAAG7B,OAAA,CAACV,UAAU;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACT,WAAW;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENjE,OAAA;cAAK6D,SAAS,EAAC,cAAc;cAAAD,QAAA,gBACzB5D,OAAA;gBAAK6D,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzB5D,OAAA;kBAAK6D,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpDjE,OAAA;kBAAK6D,SAAS,EAAC,qBAAqB;kBAAAD,QAAA,EAAC;gBAErC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNjE,OAAA;gBAAK6D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC5B5D,OAAA;kBACI6D,SAAS,EAAE,cAAclC,eAAe,CAACG,eAAe,GAAG,QAAQ,GAAG,EAAE,EAAG;kBAC3EsC,OAAO,EAAEA,CAAA,KAAMd,0BAA0B,CAAC,iBAAiB,CAAE;kBAAAM,QAAA,EAE5DjC,eAAe,CAACG,eAAe,gBAAG9B,OAAA,CAACV,UAAU;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACT,WAAW;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENjE,OAAA;cAAK6D,SAAS,EAAC,cAAc;cAAAD,QAAA,gBACzB5D,OAAA;gBAAK6D,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzB5D,OAAA;kBAAK6D,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDjE,OAAA;kBAAK6D,SAAS,EAAC,qBAAqB;kBAAAD,QAAA,EAAC;gBAErC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNjE,OAAA;gBAAK6D,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,eAC5B5D,OAAA;kBACI6D,SAAS,EAAE,cAAclC,eAAe,CAACI,mBAAmB,GAAG,QAAQ,GAAG,EAAE,EAAG;kBAC/EqC,OAAO,EAAEA,CAAA,KAAMd,0BAA0B,CAAC,qBAAqB,CAAE;kBAAAM,QAAA,EAEhEjC,eAAe,CAACI,mBAAmB,gBAAG/B,OAAA,CAACV,UAAU;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGjE,OAAA,CAACT,WAAW;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNjE,OAAA;UAAK6D,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAC1B5D,OAAA;YAAK6D,SAAS,EAAC,sBAAsB;YAAAD,QAAA,gBACjC5D,OAAA;cAAA4D,QAAA,gBAAI5D,OAAA,CAACJ,YAAY;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yBAAqB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CjE,OAAA;cAAA4D,QAAA,EAAG;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eAENjE,OAAA;YAAK6D,SAAS,EAAC,uBAAuB;YAAAD,QAAA,eAClC5D,OAAA;cAAK6D,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC1B5D,OAAA;gBAAK6D,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzB5D,OAAA,CAACf,WAAW;kBAAC4E,SAAS,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpCjE,OAAA;kBAAA4D,QAAA,gBACI5D,OAAA;oBAAA4D,QAAA,EAAQ;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,oFAChC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNjE,OAAA;gBAAK6D,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzB5D,OAAA,CAACb,UAAU;kBAAC0E,SAAS,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnCjE,OAAA;kBAAA4D,QAAA,gBACI5D,OAAA;oBAAA4D,QAAA,EAAQ;kBAAiB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gEACtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNjE,OAAA;gBAAK6D,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBACzB5D,OAAA,CAACd,KAAK;kBAAC2E,SAAS,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9BjE,OAAA;kBAAA4D,QAAA,gBACI5D,OAAA;oBAAA4D,QAAA,EAAQ;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,8DACrC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB;AAAC3D,EAAA,CArXQD,YAAY;EAAA,QACAtB,WAAW;AAAA;AAAAuF,EAAA,GADvBjE,YAAY;AAuXrB,eAAeA,YAAY;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}