[{"C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js": "1", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js": "2", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js": "3", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js": "4", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js": "5", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js": "6", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js": "7", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js": "8", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js": "9", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js": "10", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js": "11", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js": "12", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js": "13", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js": "14", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js": "15", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js": "16", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js": "17", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js": "18", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js": "19", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js": "20", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js": "21", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js": "22", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js": "23", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js": "24", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js": "25", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js": "26", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js": "27", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js": "28", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js": "29", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js": "30", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js": "31", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js": "32", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js": "33", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js": "34", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js": "35", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js": "36", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js": "37", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js": "38", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js": "39", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js": "40", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js": "41", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js": "42", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js": "43", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js": "44", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js": "45", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js": "46", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js": "47", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js": "48", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js": "49", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js": "50", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js": "51", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js": "52", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js": "53", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js": "54", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js": "55", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js": "56", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js": "57", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js": "58", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js": "59", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js": "60", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js": "61", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js": "62", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js": "63", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js": "64", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js": "65", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js": "66", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js": "67", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js": "68", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js": "69", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js": "70", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js": "71", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js": "72", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js": "73", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js": "74", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js": "75", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js": "76", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js": "77", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js": "78", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js": "79", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js": "80", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js": "81", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js": "82", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js": "83", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js": "84", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js": "85", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js": "86", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js": "87", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js": "88", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js": "89", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AuthLayout.js": "90", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js": "91", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js": "92", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js": "93", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js": "94", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js": "95", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js": "96", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js": "97", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js": "98", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js": "99", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CurrencyManagement.js": "100", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\index.js": "101", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencySelector.js": "102", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencyAmount.js": "103", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TestLogin.js": "104", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\faviconUtils.js": "105"}, {"size": 1593, "mtime": 1739215325917, "results": "106", "hashOfConfig": "107"}, {"size": 11978, "mtime": 1751221621390, "results": "108", "hashOfConfig": "107"}, {"size": 362, "mtime": 1725527312699, "results": "109", "hashOfConfig": "107"}, {"size": 3418, "mtime": 1751289023276, "results": "110", "hashOfConfig": "107"}, {"size": 1583, "mtime": 1738380484748, "results": "111", "hashOfConfig": "107"}, {"size": 1958, "mtime": 1738907546846, "results": "112", "hashOfConfig": "107"}, {"size": 1431, "mtime": 1747472215947, "results": "113", "hashOfConfig": "107"}, {"size": 18387, "mtime": 1751314962308, "results": "114", "hashOfConfig": "107"}, {"size": 5798, "mtime": 1751306691860, "results": "115", "hashOfConfig": "107"}, {"size": 10946, "mtime": 1749736717528, "results": "116", "hashOfConfig": "107"}, {"size": 63994, "mtime": 1751297486890, "results": "117", "hashOfConfig": "107"}, {"size": 34669, "mtime": 1749746085318, "results": "118", "hashOfConfig": "107"}, {"size": 10808, "mtime": 1751196036202, "results": "119", "hashOfConfig": "107"}, {"size": 18492, "mtime": 1751297568375, "results": "120", "hashOfConfig": "107"}, {"size": 25373, "mtime": 1751220003165, "results": "121", "hashOfConfig": "107"}, {"size": 41023, "mtime": 1749746085319, "results": "122", "hashOfConfig": "107"}, {"size": 16711, "mtime": 1751220332449, "results": "123", "hashOfConfig": "107"}, {"size": 43308, "mtime": 1749118478833, "results": "124", "hashOfConfig": "107"}, {"size": 14068, "mtime": 1751297498169, "results": "125", "hashOfConfig": "107"}, {"size": 484, "mtime": 1747774257496, "results": "126", "hashOfConfig": "107"}, {"size": 3867, "mtime": 1749105548034, "results": "127", "hashOfConfig": "107"}, {"size": 19635, "mtime": 1751297527768, "results": "128", "hashOfConfig": "107"}, {"size": 15481, "mtime": 1751290367000, "results": "129", "hashOfConfig": "107"}, {"size": 6470, "mtime": 1751306633077, "results": "130", "hashOfConfig": "107"}, {"size": 398, "mtime": 1725625029363, "results": "131", "hashOfConfig": "107"}, {"size": 10599, "mtime": 1749746085318, "results": "132", "hashOfConfig": "107"}, {"size": 30545, "mtime": 1751297583377, "results": "133", "hashOfConfig": "107"}, {"size": 37419, "mtime": 1751297458472, "results": "134", "hashOfConfig": "107"}, {"size": 22302, "mtime": 1751297597725, "results": "135", "hashOfConfig": "107"}, {"size": 9112, "mtime": 1734251655762, "results": "136", "hashOfConfig": "107"}, {"size": 11975, "mtime": 1749736717528, "results": "137", "hashOfConfig": "107"}, {"size": 13002, "mtime": 1751295464370, "results": "138", "hashOfConfig": "107"}, {"size": 12046, "mtime": 1751297683810, "results": "139", "hashOfConfig": "107"}, {"size": 15644, "mtime": 1751220144047, "results": "140", "hashOfConfig": "107"}, {"size": 18020, "mtime": 1751314931751, "results": "141", "hashOfConfig": "107"}, {"size": 12891, "mtime": 1749736717512, "results": "142", "hashOfConfig": "107"}, {"size": 29744, "mtime": 1751306674927, "results": "143", "hashOfConfig": "107"}, {"size": 5321, "mtime": 1751306646939, "results": "144", "hashOfConfig": "107"}, {"size": 205, "mtime": 1732832805260, "results": "145", "hashOfConfig": "107"}, {"size": 28050, "mtime": 1738011980316, "results": "146", "hashOfConfig": "107"}, {"size": 31265, "mtime": 1751290174162, "results": "147", "hashOfConfig": "107"}, {"size": 8917, "mtime": 1738228976181, "results": "148", "hashOfConfig": "107"}, {"size": 1242, "mtime": 1732832820214, "results": "149", "hashOfConfig": "107"}, {"size": 8290, "mtime": 1751193168091, "results": "150", "hashOfConfig": "107"}, {"size": 1098, "mtime": 1732832839965, "results": "151", "hashOfConfig": "107"}, {"size": 11530, "mtime": 1732983571250, "results": "152", "hashOfConfig": "107"}, {"size": 23387, "mtime": 1751295428183, "results": "153", "hashOfConfig": "107"}, {"size": 24442, "mtime": 1751297418539, "results": "154", "hashOfConfig": "107"}, {"size": 4310, "mtime": 1734245942035, "results": "155", "hashOfConfig": "107"}, {"size": 5623, "mtime": 1734245958195, "results": "156", "hashOfConfig": "107"}, {"size": 3339, "mtime": 1734245925091, "results": "157", "hashOfConfig": "107"}, {"size": 6576, "mtime": 1751295326646, "results": "158", "hashOfConfig": "107"}, {"size": 5681, "mtime": 1734287339563, "results": "159", "hashOfConfig": "107"}, {"size": 10920, "mtime": 1739168463615, "results": "160", "hashOfConfig": "107"}, {"size": 14257, "mtime": 1739212427178, "results": "161", "hashOfConfig": "107"}, {"size": 16886, "mtime": 1751297275313, "results": "162", "hashOfConfig": "107"}, {"size": 21457, "mtime": 1751289936798, "results": "163", "hashOfConfig": "107"}, {"size": 3211, "mtime": 1747478622718, "results": "164", "hashOfConfig": "107"}, {"size": 7496, "mtime": 1751193097200, "results": "165", "hashOfConfig": "107"}, {"size": 1352, "mtime": 1738907631772, "results": "166", "hashOfConfig": "107"}, {"size": 591, "mtime": 1737714035353, "results": "167", "hashOfConfig": "107"}, {"size": 4889, "mtime": 1739089917990, "results": "168", "hashOfConfig": "107"}, {"size": 4026, "mtime": 1749114060143, "results": "169", "hashOfConfig": "107"}, {"size": 2690, "mtime": 1749742809442, "results": "170", "hashOfConfig": "107"}, {"size": 597, "mtime": 1738005020143, "results": "171", "hashOfConfig": "107"}, {"size": 2649, "mtime": 1745558530865, "results": "172", "hashOfConfig": "107"}, {"size": 856, "mtime": 1738005002533, "results": "173", "hashOfConfig": "107"}, {"size": 778, "mtime": 1737703033090, "results": "174", "hashOfConfig": "107"}, {"size": 9268, "mtime": 1739089382382, "results": "175", "hashOfConfig": "107"}, {"size": 4473, "mtime": 1749736717521, "results": "176", "hashOfConfig": "107"}, {"size": 6511, "mtime": 1747772230646, "results": "177", "hashOfConfig": "107"}, {"size": 3561, "mtime": 1747465926259, "results": "178", "hashOfConfig": "107"}, {"size": 2058, "mtime": 1745560016985, "results": "179", "hashOfConfig": "107"}, {"size": 3270, "mtime": 1747683592095, "results": "180", "hashOfConfig": "107"}, {"size": 21913, "mtime": 1751279289062, "results": "181", "hashOfConfig": "107"}, {"size": 35666, "mtime": 1749736717528, "results": "182", "hashOfConfig": "107"}, {"size": 17158, "mtime": 1749113919875, "results": "183", "hashOfConfig": "107"}, {"size": 12332, "mtime": 1749106493490, "results": "184", "hashOfConfig": "107"}, {"size": 2047, "mtime": 1751271203937, "results": "185", "hashOfConfig": "107"}, {"size": 35110, "mtime": 1749118685592, "results": "186", "hashOfConfig": "107"}, {"size": 17866, "mtime": 1751287911119, "results": "187", "hashOfConfig": "107"}, {"size": 317, "mtime": 1749231241721, "results": "188", "hashOfConfig": "107"}, {"size": 22414, "mtime": 1749237247953, "results": "189", "hashOfConfig": "107"}, {"size": 15311, "mtime": 1749239054836, "results": "190", "hashOfConfig": "107"}, {"size": 19477, "mtime": 1749236876615, "results": "191", "hashOfConfig": "107"}, {"size": 12103, "mtime": 1749736717523, "results": "192", "hashOfConfig": "107"}, {"size": 26689, "mtime": 1749237355112, "results": "193", "hashOfConfig": "107"}, {"size": 16808, "mtime": 1751306663144, "results": "194", "hashOfConfig": "107"}, {"size": 3567, "mtime": 1749282608572, "results": "195", "hashOfConfig": "107"}, {"size": 3229, "mtime": 1749749514923, "results": "196", "hashOfConfig": "107"}, {"size": 13936, "mtime": 1749750686297, "results": "197", "hashOfConfig": "107"}, {"size": 1833, "mtime": 1749750397933, "results": "198", "hashOfConfig": "107"}, {"size": 9434, "mtime": 1751190331897, "results": "199", "hashOfConfig": "107"}, {"size": 3176, "mtime": 1751190174797, "results": "200", "hashOfConfig": "107"}, {"size": 5569, "mtime": 1751190153517, "results": "201", "hashOfConfig": "107"}, {"size": 4644, "mtime": 1751190097589, "results": "202", "hashOfConfig": "107"}, {"size": 6877, "mtime": 1751190061103, "results": "203", "hashOfConfig": "107"}, {"size": 5619, "mtime": 1751190125112, "results": "204", "hashOfConfig": "107"}, {"size": 5823, "mtime": 1751190205858, "results": "205", "hashOfConfig": "107"}, {"size": 31995, "mtime": 1751217647037, "results": "206", "hashOfConfig": "107"}, {"size": 977, "mtime": 1749746063985, "results": "207", "hashOfConfig": "107"}, {"size": 8985, "mtime": 1751206912079, "results": "208", "hashOfConfig": "107"}, {"size": 7542, "mtime": 1751190440918, "results": "209", "hashOfConfig": "107"}, {"size": 7418, "mtime": 1751224969690, "results": "210", "hashOfConfig": "107"}, {"size": 4230, "mtime": 1751271092642, "results": "211", "hashOfConfig": "107"}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eamnk2", {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js", ["527"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js", ["528"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js", ["529", "530", "531"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js", ["532", "533"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js", ["534", "535", "536", "537", "538", "539", "540", "541", "542", "543"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js", ["544", "545", "546"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js", ["547", "548"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js", ["549", "550", "551", "552", "553"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js", ["554"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js", ["555", "556"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js", ["557"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js", ["558", "559"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js", ["560"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js", ["561", "562", "563", "564"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js", ["565", "566", "567", "568"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js", ["569"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js", ["570", "571", "572"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js", ["573"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js", ["574", "575"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js", ["576"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js", ["577", "578", "579", "580", "581"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js", ["582"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js", ["583", "584", "585"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js", ["586"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js", ["587", "588", "589", "590", "591"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js", ["592", "593", "594", "595", "596"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js", ["597"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js", ["598"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js", ["599"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js", ["600", "601"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js", ["602", "603", "604", "605"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js", ["606"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js", ["607", "608"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js", ["609", "610"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js", ["611", "612", "613"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js", ["614"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js", ["615", "616", "617"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js", ["618"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js", ["619", "620"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js", ["621", "622", "623", "624"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js", ["625", "626"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js", ["627", "628"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AuthLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js", ["629"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js", ["630", "631"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CurrencyManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencySelector.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencyAmount.js", ["632"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TestLogin.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\faviconUtils.js", [], [], {"ruleId": "633", "severity": 1, "message": "634", "line": 72, "column": 8, "nodeType": "635", "messageId": "636", "endLine": 72, "endColumn": 23}, {"ruleId": "637", "severity": 1, "message": "638", "line": 31, "column": 6, "nodeType": "639", "endLine": 31, "endColumn": 8, "suggestions": "640"}, {"ruleId": "637", "severity": 1, "message": "641", "line": 27, "column": 9, "nodeType": "642", "endLine": 27, "endColumn": 62}, {"ruleId": "637", "severity": 1, "message": "643", "line": 75, "column": 6, "nodeType": "639", "endLine": 75, "endColumn": 50, "suggestions": "644"}, {"ruleId": "637", "severity": 1, "message": "645", "line": 135, "column": 6, "nodeType": "639", "endLine": 135, "endColumn": 103, "suggestions": "646"}, {"ruleId": "633", "severity": 1, "message": "647", "line": 4, "column": 121, "nodeType": "635", "messageId": "636", "endLine": 4, "endColumn": 132}, {"ruleId": "633", "severity": 1, "message": "648", "line": 11, "column": 11, "nodeType": "635", "messageId": "636", "endLine": 11, "endColumn": 19}, {"ruleId": "633", "severity": 1, "message": "649", "line": 8, "column": 3, "nodeType": "635", "messageId": "636", "endLine": 8, "endColumn": 16}, {"ruleId": "633", "severity": 1, "message": "650", "line": 10, "column": 3, "nodeType": "635", "messageId": "636", "endLine": 10, "endColumn": 17}, {"ruleId": "633", "severity": 1, "message": "651", "line": 11, "column": 3, "nodeType": "635", "messageId": "636", "endLine": 11, "endColumn": 13}, {"ruleId": "633", "severity": 1, "message": "652", "line": 12, "column": 3, "nodeType": "635", "messageId": "636", "endLine": 12, "endColumn": 16}, {"ruleId": "633", "severity": 1, "message": "653", "line": 13, "column": 3, "nodeType": "635", "messageId": "636", "endLine": 13, "endColumn": 8}, {"ruleId": "633", "severity": 1, "message": "654", "line": 14, "column": 3, "nodeType": "635", "messageId": "636", "endLine": 14, "endColumn": 16}, {"ruleId": "633", "severity": 1, "message": "655", "line": 15, "column": 3, "nodeType": "635", "messageId": "636", "endLine": 15, "endColumn": 10}, {"ruleId": "633", "severity": 1, "message": "656", "line": 16, "column": 3, "nodeType": "635", "messageId": "636", "endLine": 16, "endColumn": 9}, {"ruleId": "633", "severity": 1, "message": "657", "line": 20, "column": 16, "nodeType": "635", "messageId": "636", "endLine": 20, "endColumn": 19}, {"ruleId": "633", "severity": 1, "message": "658", "line": 47, "column": 12, "nodeType": "635", "messageId": "636", "endLine": 47, "endColumn": 30}, {"ruleId": "633", "severity": 1, "message": "659", "line": 5, "column": 23, "nodeType": "635", "messageId": "636", "endLine": 5, "endColumn": 38}, {"ruleId": "633", "severity": 1, "message": "660", "line": 10, "column": 7, "nodeType": "635", "messageId": "636", "endLine": 10, "endColumn": 19}, {"ruleId": "637", "severity": 1, "message": "661", "line": 33, "column": 8, "nodeType": "639", "endLine": 33, "endColumn": 10, "suggestions": "662"}, {"ruleId": "633", "severity": 1, "message": "663", "line": 20, "column": 10, "nodeType": "635", "messageId": "636", "endLine": 20, "endColumn": 20}, {"ruleId": "633", "severity": 1, "message": "664", "line": 20, "column": 22, "nodeType": "635", "messageId": "636", "endLine": 20, "endColumn": 35}, {"ruleId": "633", "severity": 1, "message": "665", "line": 3, "column": 20, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 28}, {"ruleId": "633", "severity": 1, "message": "666", "line": 3, "column": 37, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 43}, {"ruleId": "633", "severity": 1, "message": "667", "line": 3, "column": 45, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 52}, {"ruleId": "633", "severity": 1, "message": "668", "line": 13, "column": 21, "nodeType": "635", "messageId": "636", "endLine": 13, "endColumn": 31}, {"ruleId": "637", "severity": 1, "message": "669", "line": 40, "column": 8, "nodeType": "639", "endLine": 40, "endColumn": 41, "suggestions": "670"}, {"ruleId": "633", "severity": 1, "message": "671", "line": 17, "column": 12, "nodeType": "635", "messageId": "636", "endLine": 17, "endColumn": 21}, {"ruleId": "633", "severity": 1, "message": "665", "line": 3, "column": 20, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 28}, {"ruleId": "637", "severity": 1, "message": "672", "line": 39, "column": 8, "nodeType": "639", "endLine": 39, "endColumn": 42, "suggestions": "673"}, {"ruleId": "633", "severity": 1, "message": "674", "line": 3, "column": 34, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 41}, {"ruleId": "637", "severity": 1, "message": "675", "line": 23, "column": 8, "nodeType": "639", "endLine": 23, "endColumn": 19, "suggestions": "676"}, {"ruleId": "637", "severity": 1, "message": "677", "line": 32, "column": 8, "nodeType": "639", "endLine": 32, "endColumn": 30, "suggestions": "678"}, {"ruleId": "637", "severity": 1, "message": "679", "line": 43, "column": 8, "nodeType": "639", "endLine": 43, "endColumn": 10, "suggestions": "680"}, {"ruleId": "633", "severity": 1, "message": "681", "line": 11, "column": 5, "nodeType": "635", "messageId": "636", "endLine": 11, "endColumn": 20}, {"ruleId": "633", "severity": 1, "message": "682", "line": 16, "column": 5, "nodeType": "635", "messageId": "636", "endLine": 16, "endColumn": 12}, {"ruleId": "633", "severity": 1, "message": "683", "line": 17, "column": 5, "nodeType": "635", "messageId": "636", "endLine": 17, "endColumn": 17}, {"ruleId": "633", "severity": 1, "message": "674", "line": 18, "column": 5, "nodeType": "635", "messageId": "636", "endLine": 18, "endColumn": 12}, {"ruleId": "633", "severity": 1, "message": "684", "line": 1, "column": 60, "nodeType": "635", "messageId": "636", "endLine": 1, "endColumn": 66}, {"ruleId": "633", "severity": 1, "message": "685", "line": 28, "column": 12, "nodeType": "635", "messageId": "636", "endLine": 28, "endColumn": 26}, {"ruleId": "633", "severity": 1, "message": "686", "line": 28, "column": 28, "nodeType": "635", "messageId": "636", "endLine": 28, "endColumn": 45}, {"ruleId": "637", "severity": 1, "message": "687", "line": 83, "column": 8, "nodeType": "639", "endLine": 83, "endColumn": 10, "suggestions": "688"}, {"ruleId": "633", "severity": 1, "message": "689", "line": 4, "column": 10, "nodeType": "635", "messageId": "636", "endLine": 4, "endColumn": 22}, {"ruleId": "633", "severity": 1, "message": "647", "line": 3, "column": 19, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 30}, {"ruleId": "633", "severity": 1, "message": "690", "line": 14, "column": 12, "nodeType": "635", "messageId": "636", "endLine": 14, "endColumn": 24}, {"ruleId": "633", "severity": 1, "message": "691", "line": 93, "column": 11, "nodeType": "635", "messageId": "636", "endLine": 93, "endColumn": 23}, {"ruleId": "637", "severity": 1, "message": "692", "line": 84, "column": 8, "nodeType": "639", "endLine": 84, "endColumn": 18, "suggestions": "693"}, {"ruleId": "633", "severity": 1, "message": "694", "line": 113, "column": 9, "nodeType": "635", "messageId": "636", "endLine": 113, "endColumn": 25}, {"ruleId": "633", "severity": 1, "message": "695", "line": 147, "column": 9, "nodeType": "635", "messageId": "636", "endLine": 147, "endColumn": 22}, {"ruleId": "633", "severity": 1, "message": "696", "line": 4, "column": 10, "nodeType": "635", "messageId": "636", "endLine": 4, "endColumn": 17}, {"ruleId": "633", "severity": 1, "message": "697", "line": 13, "column": 10, "nodeType": "635", "messageId": "636", "endLine": 13, "endColumn": 17}, {"ruleId": "633", "severity": 1, "message": "698", "line": 14, "column": 10, "nodeType": "635", "messageId": "636", "endLine": 14, "endColumn": 15}, {"ruleId": "633", "severity": 1, "message": "695", "line": 115, "column": 9, "nodeType": "635", "messageId": "636", "endLine": 115, "endColumn": 22}, {"ruleId": "633", "severity": 1, "message": "699", "line": 132, "column": 9, "nodeType": "635", "messageId": "636", "endLine": 132, "endColumn": 19}, {"ruleId": "633", "severity": 1, "message": "700", "line": 145, "column": 9, "nodeType": "635", "messageId": "636", "endLine": 145, "endColumn": 22}, {"ruleId": "637", "severity": 1, "message": "672", "line": 44, "column": 8, "nodeType": "639", "endLine": 44, "endColumn": 21, "suggestions": "701"}, {"ruleId": "633", "severity": 1, "message": "702", "line": 10, "column": 10, "nodeType": "635", "messageId": "636", "endLine": 10, "endColumn": 23}, {"ruleId": "633", "severity": 1, "message": "703", "line": 10, "column": 25, "nodeType": "635", "messageId": "636", "endLine": 10, "endColumn": 41}, {"ruleId": "633", "severity": 1, "message": "704", "line": 18, "column": 25, "nodeType": "635", "messageId": "636", "endLine": 18, "endColumn": 46}, {"ruleId": "637", "severity": 1, "message": "705", "line": 19, "column": 8, "nodeType": "639", "endLine": 19, "endColumn": 10, "suggestions": "706"}, {"ruleId": "633", "severity": 1, "message": "689", "line": 4, "column": 10, "nodeType": "635", "messageId": "636", "endLine": 4, "endColumn": 22}, {"ruleId": "633", "severity": 1, "message": "707", "line": 7, "column": 14, "nodeType": "635", "messageId": "636", "endLine": 7, "endColumn": 20}, {"ruleId": "633", "severity": 1, "message": "683", "line": 7, "column": 41, "nodeType": "635", "messageId": "636", "endLine": 7, "endColumn": 53}, {"ruleId": "633", "severity": 1, "message": "708", "line": 8, "column": 46, "nodeType": "635", "messageId": "636", "endLine": 8, "endColumn": 52}, {"ruleId": "633", "severity": 1, "message": "709", "line": 306, "column": 11, "nodeType": "635", "messageId": "636", "endLine": 306, "endColumn": 27}, {"ruleId": "633", "severity": 1, "message": "710", "line": 4, "column": 45, "nodeType": "635", "messageId": "636", "endLine": 4, "endColumn": 64}, {"ruleId": "633", "severity": 1, "message": "711", "line": 4, "column": 66, "nodeType": "635", "messageId": "636", "endLine": 4, "endColumn": 79}, {"ruleId": "633", "severity": 1, "message": "712", "line": 4, "column": 111, "nodeType": "635", "messageId": "636", "endLine": 4, "endColumn": 123}, {"ruleId": "637", "severity": 1, "message": "713", "line": 29, "column": 8, "nodeType": "639", "endLine": 29, "endColumn": 10, "suggestions": "714"}, {"ruleId": "633", "severity": 1, "message": "715", "line": 256, "column": 11, "nodeType": "635", "messageId": "636", "endLine": 256, "endColumn": 26}, {"ruleId": "637", "severity": 1, "message": "716", "line": 24, "column": 8, "nodeType": "639", "endLine": 24, "endColumn": 33, "suggestions": "717"}, {"ruleId": "633", "severity": 1, "message": "707", "line": 5, "column": 57, "nodeType": "635", "messageId": "636", "endLine": 5, "endColumn": 63}, {"ruleId": "637", "severity": 1, "message": "718", "line": 24, "column": 8, "nodeType": "639", "endLine": 24, "endColumn": 33, "suggestions": "719"}, {"ruleId": "633", "severity": 1, "message": "720", "line": 121, "column": 19, "nodeType": "635", "messageId": "636", "endLine": 121, "endColumn": 28}, {"ruleId": "633", "severity": 1, "message": "721", "line": 136, "column": 19, "nodeType": "635", "messageId": "636", "endLine": 136, "endColumn": 22}, {"ruleId": "633", "severity": 1, "message": "722", "line": 4, "column": 5, "nodeType": "635", "messageId": "636", "endLine": 4, "endColumn": 14}, {"ruleId": "633", "severity": 1, "message": "723", "line": 6, "column": 5, "nodeType": "635", "messageId": "636", "endLine": 6, "endColumn": 10}, {"ruleId": "633", "severity": 1, "message": "651", "line": 7, "column": 5, "nodeType": "635", "messageId": "636", "endLine": 7, "endColumn": 15}, {"ruleId": "633", "severity": 1, "message": "724", "line": 8, "column": 5, "nodeType": "635", "messageId": "636", "endLine": 8, "endColumn": 16}, {"ruleId": "633", "severity": 1, "message": "725", "line": 57, "column": 9, "nodeType": "635", "messageId": "636", "endLine": 57, "endColumn": 26}, {"ruleId": "633", "severity": 1, "message": "650", "line": 2, "column": 10, "nodeType": "635", "messageId": "636", "endLine": 2, "endColumn": 24}, {"ruleId": "633", "severity": 1, "message": "726", "line": 2, "column": 26, "nodeType": "635", "messageId": "636", "endLine": 2, "endColumn": 34}, {"ruleId": "633", "severity": 1, "message": "727", "line": 3, "column": 35, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 41}, {"ruleId": "633", "severity": 1, "message": "728", "line": 5, "column": 25, "nodeType": "635", "messageId": "636", "endLine": 5, "endColumn": 39}, {"ruleId": "633", "severity": 1, "message": "729", "line": 5, "column": 5, "nodeType": "635", "messageId": "636", "endLine": 5, "endColumn": 15}, {"ruleId": "633", "severity": 1, "message": "649", "line": 6, "column": 5, "nodeType": "635", "messageId": "636", "endLine": 6, "endColumn": 18}, {"ruleId": "637", "severity": 1, "message": "730", "line": 29, "column": 8, "nodeType": "639", "endLine": 29, "endColumn": 31, "suggestions": "731"}, {"ruleId": "637", "severity": 1, "message": "672", "line": 31, "column": 8, "nodeType": "639", "endLine": 31, "endColumn": 17, "suggestions": "732"}, {"ruleId": "633", "severity": 1, "message": "733", "line": 3, "column": 33, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 38}, {"ruleId": "633", "severity": 1, "message": "734", "line": 14, "column": 12, "nodeType": "635", "messageId": "636", "endLine": 14, "endColumn": 21}, {"ruleId": "637", "severity": 1, "message": "735", "line": 29, "column": 8, "nodeType": "639", "endLine": 29, "endColumn": 10, "suggestions": "736"}, {"ruleId": "633", "severity": 1, "message": "737", "line": 3, "column": 41, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 62}, {"ruleId": "633", "severity": 1, "message": "737", "line": 3, "column": 40, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 61}, {"ruleId": "637", "severity": 1, "message": "738", "line": 28, "column": 8, "nodeType": "639", "endLine": 28, "endColumn": 24, "suggestions": "739"}, {"ruleId": "633", "severity": 1, "message": "674", "line": 3, "column": 51, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 58}, {"ruleId": "633", "severity": 1, "message": "723", "line": 3, "column": 102, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 107}, {"ruleId": "633", "severity": 1, "message": "740", "line": 18, "column": 12, "nodeType": "635", "messageId": "636", "endLine": 18, "endColumn": 21}, {"ruleId": "637", "severity": 1, "message": "741", "line": 23, "column": 8, "nodeType": "639", "endLine": 23, "endColumn": 17, "suggestions": "742"}, {"ruleId": "633", "severity": 1, "message": "743", "line": 3, "column": 40, "nodeType": "635", "messageId": "636", "endLine": 3, "endColumn": 46}, {"ruleId": "637", "severity": 1, "message": "744", "line": 30, "column": 8, "nodeType": "639", "endLine": 30, "endColumn": 10, "suggestions": "745"}, {"ruleId": "633", "severity": 1, "message": "746", "line": 6, "column": 5, "nodeType": "635", "messageId": "636", "endLine": 6, "endColumn": 11}, {"ruleId": "637", "severity": 1, "message": "747", "line": 38, "column": 8, "nodeType": "639", "endLine": 38, "endColumn": 16, "suggestions": "748"}, {"ruleId": "637", "severity": 1, "message": "749", "line": 51, "column": 8, "nodeType": "639", "endLine": 51, "endColumn": 10, "suggestions": "750"}, {"ruleId": "751", "severity": 1, "message": "752", "line": 93, "column": 39, "nodeType": "753", "messageId": "754", "endLine": 93, "endColumn": 86}, {"ruleId": "633", "severity": 1, "message": "755", "line": 177, "column": 15, "nodeType": "635", "messageId": "636", "endLine": 177, "endColumn": 30}, {"ruleId": "633", "severity": 1, "message": "756", "line": 144, "column": 36, "nodeType": "635", "messageId": "636", "endLine": 144, "endColumn": 48}, "no-unused-vars", "'LeagueSelection' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'removeError'. Either include it or remove the dependency array.", "ArrayExpression", ["757"], "The 'publicRoutes' array makes the dependencies of useCallback Hook (at line 110) change on every render. To fix this, wrap the initialization of 'publicRoutes' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'setUserData'. Either include it or remove the dependency array.", ["758"], "React Hook useEffect has a missing dependency: 'publicRoutes'. Either include it or remove the dependency array.", ["759"], "'FaChartLine' is defined but never used.", "'navigate' is assigned a value but never used.", "'FaCalendarAlt' is defined but never used.", "'FaFootballBall' is defined but never used.", "'FaChartBar' is defined but never used.", "'FaExchangeAlt' is defined but never used.", "'FaEye' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'FaClock' is defined but never used.", "'FaBell' is defined but never used.", "'Bar' is defined but never used.", "'teamPopularityData' is assigned a value but never used.", "'currencyService' is defined but never used.", "'API_BASE_URL' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTeams'. Either include it or remove the dependency array.", ["760"], "'challenges' is assigned a value but never used.", "'setChallenges' is assigned a value but never used.", "'FaFilter' is defined but never used.", "'FaEdit' is defined but never used.", "'FaTrash' is defined but never used.", "'setSuccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllBets'. Either include it or remove the dependency array.", ["761"], "'otpExpiry' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLeaderboard'. Either include it or remove the dependency array.", ["762"], "'FaTimes' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchConversations'. Either include it or remove the dependency array.", ["763"], "React Hook useEffect has missing dependencies: 'fetchMessages' and 'markMessagesAsRead'. Either include them or remove the dependency array.", ["764"], "React Hook useEffect has a missing dependency: 'checkChallengeStatus'. Either include it or remove the dependency array.", ["765"], "'FaMoneyBillWave' is defined but never used.", "'FaUsers' is defined but never used.", "'FaInfoCircle' is defined but never used.", "'useRef' is defined but never used.", "'payoutPreviews' is assigned a value but never used.", "'setPayoutPreviews' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'fetchLeagueDetails'. Either include it or remove the dependency array.", ["766"], "'API_BASE_URL' is defined but never used.", "'loadingUsers' is assigned a value but never used.", "'currentUsers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserData'. Either include it or remove the dependency array.", ["767"], "'renderPagination' is assigned a value but never used.", "'calculateOdds' is assigned a value but never used.", "'FaCoins' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'formatDate' is assigned a value but never used.", "'getUserStatus' is assigned a value but never used.", ["768"], "'paymentMethod' is assigned a value but never used.", "'setPaymentMethod' is assigned a value but never used.", "'convertToUserCurrency' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPendingRequests'. Either include it or remove the dependency array.", ["769"], "'FaStar' is defined but never used.", "'FaFire' is defined but never used.", "'renderNavigation' is assigned a value but never used.", "'FaExclamationCircle' is defined but never used.", "'FaUserFriends' is defined but never used.", "'FaUserCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFriends'. Either include it or remove the dependency array.", ["770"], "'handleChallenge' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["771"], "React Hook useEffect has a missing dependency: 'fetchCreditRequests'. Either include it or remove the dependency array.", ["772"], "'matchDate' is assigned a value but never used.", "'now' is assigned a value but never used.", "'FaGamepad' is defined but never used.", "'FaCog' is defined but never used.", "'FaMoneyBill' is defined but never used.", "'handleLogoutClick' is assigned a value but never used.", "'FaTrophy' is defined but never used.", "'FaSave' is defined but never used.", "'refreshFavicon' is defined but never used.", "'FaDownload' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReport'. Either include it or remove the dependency array.", ["773"], ["774"], "'FaKey' is defined but never used.", "'secretKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initiate2FASetup'. Either include it or remove the dependency array.", ["775"], "'FaExclamationTriangle' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendInitialOTP'. Either include it or remove the dependency array.", ["776"], "'adminInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPreferences'. Either include it or remove the dependency array.", ["777"], "'FaCopy' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["778"], "'FaUser' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchUserBets', 'fetchUserDetails', and 'fetchUserTransactions'. Either include them or remove the dependency array.", ["779"], "React Hook useCallback has a missing dependency: 'CACHE_DURATION'. Either include it or remove the dependency array.", ["780"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'attempt'.", "ArrowFunctionExpression", "unsafeRefs", "'originalExecute' is assigned a value but never used.", "'userCurrency' is assigned a value but never used.", {"desc": "781", "fix": "782"}, {"desc": "783", "fix": "784"}, {"desc": "785", "fix": "786"}, {"desc": "787", "fix": "788"}, {"desc": "789", "fix": "790"}, {"desc": "791", "fix": "792"}, {"desc": "793", "fix": "794"}, {"desc": "795", "fix": "796"}, {"desc": "797", "fix": "798"}, {"desc": "799", "fix": "800"}, {"desc": "801", "fix": "802"}, {"desc": "803", "fix": "804"}, {"desc": "805", "fix": "806"}, {"desc": "807", "fix": "808"}, {"desc": "809", "fix": "810"}, {"desc": "811", "fix": "812"}, {"desc": "813", "fix": "814"}, {"desc": "815", "fix": "816"}, {"desc": "817", "fix": "818"}, {"desc": "819", "fix": "820"}, {"desc": "821", "fix": "822"}, {"desc": "823", "fix": "824"}, {"desc": "825", "fix": "826"}, {"desc": "827", "fix": "828"}, "Update the dependencies array to be: [removeError]", {"range": "829", "text": "830"}, "Update the dependencies array to be: [token, userId, setUserData, navigate, location.pathname]", {"range": "831", "text": "832"}, "Update the dependencies array to be: [token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, fetchSidebarLogo, publicRoutes]", {"range": "833", "text": "834"}, "Update the dependencies array to be: [fetchTeams]", {"range": "835", "text": "836"}, "Update the dependencies array to be: [pagination.currentPage, filters, fetchAllBets]", {"range": "837", "text": "838"}, "Update the dependencies array to be: [pagination.current_page, filters, fetchLeaderboard]", {"range": "839", "text": "840"}, "Update the dependencies array to be: [activeTab, fetchConversations]", {"range": "841", "text": "842"}, "Update the dependencies array to be: [fetchMessages, markMessagesAsRead, selectedConversation]", {"range": "843", "text": "844"}, "Update the dependencies array to be: [checkChallengeStatus]", {"range": "845", "text": "846"}, "Update the dependencies array to be: [fetchLeagueDetails]", {"range": "847", "text": "848"}, "Update the dependencies array to be: [fetchUserData, navigate]", {"range": "849", "text": "850"}, "Update the dependencies array to be: [currentPage, fetchLeaderboard]", {"range": "851", "text": "852"}, "Update the dependencies array to be: [fetchPendingRequests]", {"range": "853", "text": "854"}, "Update the dependencies array to be: [fetchFriends]", {"range": "855", "text": "856"}, "Update the dependencies array to be: [navigate, currentUserId, fetchInitialData]", {"range": "857", "text": "858"}, "Update the dependencies array to be: [navigate, currentUserId, fetchCreditRequests]", {"range": "859", "text": "860"}, "Update the dependencies array to be: [reportType, dateRange, fetchReport]", {"range": "861", "text": "862"}, "Update the dependencies array to be: [fetchLeaderboard, filters]", {"range": "863", "text": "864"}, "Update the dependencies array to be: [initiate2FASetup]", {"range": "865", "text": "866"}, "Update the dependencies array to be: [initialOtpSent, sendInitialOTP]", {"range": "867", "text": "868"}, "Update the dependencies array to be: [adminId, fetchPreferences]", {"range": "869", "text": "870"}, "Update the dependencies array to be: [fetchAdminData]", {"range": "871", "text": "872"}, "Update the dependencies array to be: [fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", {"range": "873", "text": "874"}, "Update the dependencies array to be: [CACHE_DURATION]", {"range": "875", "text": "876"}, [985, 987], "[removeError]", [2754, 2798], "[token, userId, setUserData, navigate, location.pathname]", [4647, 4744], "[token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, fetchSidebarLogo, publicRoutes]", [1143, 1145], "[fetchTeams]", [1193, 1226], "[pagination.currentPage, filters, fetchAllBets]", [1249, 1283], "[pagination.current_page, filters, fetchLeaderboard]", [1071, 1082], "[activeTab, fetchConversations]", [1429, 1451], "[fetch<PERSON>essages, markMessagesAsRead, selectedConversation]", [1532, 1534], "[checkChallengeStatus]", [3005, 3007], "[fetchLeagueDetails]", [3404, 3414], "[fetch<PERSON><PERSON><PERSON><PERSON>, navigate]", [1160, 1173], "[currentPage, fetchLeaderboard]", [696, 698], "[fetchPendingRequests]", [1331, 1333], "[fetchFriends]", [856, 881], "[navigate, currentUserId, fetchInitialData]", [891, 916], "[navigate, currentUserId, fetchCreditRequests]", [864, 887], "[reportType, dateRange, fetchReport]", [887, 896], "[fetchLeaderboard, filters]", [1137, 1139], "[initiate2FASetup]", [1264, 1280], "[initialOtpSent, sendInitialOTP]", [856, 865], "[adminId, fetchPreferences]", [1269, 1271], "[fetchAdminData]", [1036, 1044], "[fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", [1873, 1875], "[CACHE_DURATION]"]