[{"C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js": "1", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js": "2", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js": "3", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js": "4", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js": "5", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js": "6", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js": "7", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js": "8", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js": "9", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js": "10", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js": "11", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js": "12", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js": "13", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js": "14", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js": "15", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js": "16", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js": "17", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js": "18", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js": "19", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js": "20", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js": "21", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js": "22", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js": "23", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js": "24", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js": "25", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js": "26", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js": "27", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js": "28", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js": "29", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js": "30", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js": "31", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js": "32", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js": "33", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js": "34", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js": "35", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js": "36", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js": "37", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js": "38", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js": "39", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js": "40", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js": "41", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js": "42", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js": "43", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js": "44", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js": "45", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js": "46", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js": "47", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js": "48", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js": "49", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js": "50", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js": "51", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js": "52", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js": "53", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js": "54", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js": "55", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js": "56", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js": "57", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js": "58", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js": "59", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js": "60", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js": "61", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js": "62", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js": "63", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js": "64", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js": "65", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js": "66", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js": "67", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js": "68", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js": "69", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js": "70", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js": "71", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js": "72", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js": "73", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js": "74", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js": "75", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js": "76", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js": "77", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js": "78", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js": "79", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js": "80", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js": "81", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js": "82", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js": "83", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js": "84", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js": "85", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js": "86", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js": "87", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js": "88", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js": "89", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AuthLayout.js": "90", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js": "91", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js": "92", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js": "93", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js": "94", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js": "95", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js": "96", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js": "97", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js": "98", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js": "99", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CurrencyManagement.js": "100", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\index.js": "101", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencySelector.js": "102", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencyAmount.js": "103", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TestLogin.js": "104", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\faviconUtils.js": "105", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserSettings.js": "106", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\User2FASetup.js": "107", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserSettingsSimple.js": "108", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserSettingsFixed.js": "109"}, {"size": 1593, "mtime": 1739215325917, "results": "110", "hashOfConfig": "111"}, {"size": 12101, "mtime": 1751320880932, "results": "112", "hashOfConfig": "111"}, {"size": 362, "mtime": 1725527312699, "results": "113", "hashOfConfig": "111"}, {"size": 3418, "mtime": 1751289023276, "results": "114", "hashOfConfig": "111"}, {"size": 1583, "mtime": 1738380484748, "results": "115", "hashOfConfig": "111"}, {"size": 1958, "mtime": 1738907546846, "results": "116", "hashOfConfig": "111"}, {"size": 1431, "mtime": 1747472215947, "results": "117", "hashOfConfig": "111"}, {"size": 18387, "mtime": 1751314962308, "results": "118", "hashOfConfig": "111"}, {"size": 5798, "mtime": 1751306691860, "results": "119", "hashOfConfig": "111"}, {"size": 10946, "mtime": 1749736717528, "results": "120", "hashOfConfig": "111"}, {"size": 63994, "mtime": 1751297486890, "results": "121", "hashOfConfig": "111"}, {"size": 34669, "mtime": 1749746085318, "results": "122", "hashOfConfig": "111"}, {"size": 10808, "mtime": 1751196036202, "results": "123", "hashOfConfig": "111"}, {"size": 18492, "mtime": 1751297568375, "results": "124", "hashOfConfig": "111"}, {"size": 25373, "mtime": 1751220003165, "results": "125", "hashOfConfig": "111"}, {"size": 41023, "mtime": 1749746085319, "results": "126", "hashOfConfig": "111"}, {"size": 16711, "mtime": 1751220332449, "results": "127", "hashOfConfig": "111"}, {"size": 43308, "mtime": 1749118478833, "results": "128", "hashOfConfig": "111"}, {"size": 14068, "mtime": 1751297498169, "results": "129", "hashOfConfig": "111"}, {"size": 484, "mtime": 1747774257496, "results": "130", "hashOfConfig": "111"}, {"size": 3867, "mtime": 1749105548034, "results": "131", "hashOfConfig": "111"}, {"size": 19635, "mtime": 1751297527768, "results": "132", "hashOfConfig": "111"}, {"size": 15481, "mtime": 1751290367000, "results": "133", "hashOfConfig": "111"}, {"size": 6470, "mtime": 1751306633077, "results": "134", "hashOfConfig": "111"}, {"size": 398, "mtime": 1725625029363, "results": "135", "hashOfConfig": "111"}, {"size": 10599, "mtime": 1749746085318, "results": "136", "hashOfConfig": "111"}, {"size": 30545, "mtime": 1751297583377, "results": "137", "hashOfConfig": "111"}, {"size": 37419, "mtime": 1751297458472, "results": "138", "hashOfConfig": "111"}, {"size": 22302, "mtime": 1751297597725, "results": "139", "hashOfConfig": "111"}, {"size": 9112, "mtime": 1734251655762, "results": "140", "hashOfConfig": "111"}, {"size": 11975, "mtime": 1749736717528, "results": "141", "hashOfConfig": "111"}, {"size": 13002, "mtime": 1751295464370, "results": "142", "hashOfConfig": "111"}, {"size": 12046, "mtime": 1751297683810, "results": "143", "hashOfConfig": "111"}, {"size": 15644, "mtime": 1751220144047, "results": "144", "hashOfConfig": "111"}, {"size": 18020, "mtime": 1751314931751, "results": "145", "hashOfConfig": "111"}, {"size": 12891, "mtime": 1749736717512, "results": "146", "hashOfConfig": "111"}, {"size": 29744, "mtime": 1751306674927, "results": "147", "hashOfConfig": "111"}, {"size": 5321, "mtime": 1751306646939, "results": "148", "hashOfConfig": "111"}, {"size": 205, "mtime": 1732832805260, "results": "149", "hashOfConfig": "111"}, {"size": 28050, "mtime": 1738011980316, "results": "150", "hashOfConfig": "111"}, {"size": 31265, "mtime": 1751290174162, "results": "151", "hashOfConfig": "111"}, {"size": 8917, "mtime": 1738228976181, "results": "152", "hashOfConfig": "111"}, {"size": 1242, "mtime": 1732832820214, "results": "153", "hashOfConfig": "111"}, {"size": 8290, "mtime": 1751193168091, "results": "154", "hashOfConfig": "111"}, {"size": 1098, "mtime": 1732832839965, "results": "155", "hashOfConfig": "111"}, {"size": 11530, "mtime": 1732983571250, "results": "156", "hashOfConfig": "111"}, {"size": 23387, "mtime": 1751295428183, "results": "157", "hashOfConfig": "111"}, {"size": 24442, "mtime": 1751297418539, "results": "158", "hashOfConfig": "111"}, {"size": 4310, "mtime": 1734245942035, "results": "159", "hashOfConfig": "111"}, {"size": 5623, "mtime": 1734245958195, "results": "160", "hashOfConfig": "111"}, {"size": 3339, "mtime": 1734245925091, "results": "161", "hashOfConfig": "111"}, {"size": 6576, "mtime": 1751295326646, "results": "162", "hashOfConfig": "111"}, {"size": 5681, "mtime": 1734287339563, "results": "163", "hashOfConfig": "111"}, {"size": 10920, "mtime": 1739168463615, "results": "164", "hashOfConfig": "111"}, {"size": 14257, "mtime": 1739212427178, "results": "165", "hashOfConfig": "111"}, {"size": 16886, "mtime": 1751297275313, "results": "166", "hashOfConfig": "111"}, {"size": 21457, "mtime": 1751289936798, "results": "167", "hashOfConfig": "111"}, {"size": 3211, "mtime": 1747478622718, "results": "168", "hashOfConfig": "111"}, {"size": 7496, "mtime": 1751193097200, "results": "169", "hashOfConfig": "111"}, {"size": 1352, "mtime": 1738907631772, "results": "170", "hashOfConfig": "111"}, {"size": 591, "mtime": 1737714035353, "results": "171", "hashOfConfig": "111"}, {"size": 4889, "mtime": 1739089917990, "results": "172", "hashOfConfig": "111"}, {"size": 4026, "mtime": 1749114060143, "results": "173", "hashOfConfig": "111"}, {"size": 2690, "mtime": 1749742809442, "results": "174", "hashOfConfig": "111"}, {"size": 597, "mtime": 1738005020143, "results": "175", "hashOfConfig": "111"}, {"size": 2649, "mtime": 1745558530865, "results": "176", "hashOfConfig": "111"}, {"size": 856, "mtime": 1738005002533, "results": "177", "hashOfConfig": "111"}, {"size": 778, "mtime": 1737703033090, "results": "178", "hashOfConfig": "111"}, {"size": 9268, "mtime": 1739089382382, "results": "179", "hashOfConfig": "111"}, {"size": 4473, "mtime": 1749736717521, "results": "180", "hashOfConfig": "111"}, {"size": 6511, "mtime": 1747772230646, "results": "181", "hashOfConfig": "111"}, {"size": 3561, "mtime": 1747465926259, "results": "182", "hashOfConfig": "111"}, {"size": 2058, "mtime": 1745560016985, "results": "183", "hashOfConfig": "111"}, {"size": 3270, "mtime": 1747683592095, "results": "184", "hashOfConfig": "111"}, {"size": 21913, "mtime": 1751279289062, "results": "185", "hashOfConfig": "111"}, {"size": 35666, "mtime": 1749736717528, "results": "186", "hashOfConfig": "111"}, {"size": 17158, "mtime": 1749113919875, "results": "187", "hashOfConfig": "111"}, {"size": 12332, "mtime": 1749106493490, "results": "188", "hashOfConfig": "111"}, {"size": 2047, "mtime": 1751271203937, "results": "189", "hashOfConfig": "111"}, {"size": 35110, "mtime": 1749118685592, "results": "190", "hashOfConfig": "111"}, {"size": 17866, "mtime": 1751287911119, "results": "191", "hashOfConfig": "111"}, {"size": 317, "mtime": 1749231241721, "results": "192", "hashOfConfig": "111"}, {"size": 22414, "mtime": 1749237247953, "results": "193", "hashOfConfig": "111"}, {"size": 15311, "mtime": 1749239054836, "results": "194", "hashOfConfig": "111"}, {"size": 19477, "mtime": 1749236876615, "results": "195", "hashOfConfig": "111"}, {"size": 12103, "mtime": 1749736717523, "results": "196", "hashOfConfig": "111"}, {"size": 26689, "mtime": 1749237355112, "results": "197", "hashOfConfig": "111"}, {"size": 16808, "mtime": 1751306663144, "results": "198", "hashOfConfig": "111"}, {"size": 3567, "mtime": 1749282608572, "results": "199", "hashOfConfig": "111"}, {"size": 3229, "mtime": 1749749514923, "results": "200", "hashOfConfig": "111"}, {"size": 13936, "mtime": 1749750686297, "results": "201", "hashOfConfig": "111"}, {"size": 1833, "mtime": 1749750397933, "results": "202", "hashOfConfig": "111"}, {"size": 9434, "mtime": 1751190331897, "results": "203", "hashOfConfig": "111"}, {"size": 3176, "mtime": 1751190174797, "results": "204", "hashOfConfig": "111"}, {"size": 5569, "mtime": 1751190153517, "results": "205", "hashOfConfig": "111"}, {"size": 4644, "mtime": 1751190097589, "results": "206", "hashOfConfig": "111"}, {"size": 6877, "mtime": 1751190061103, "results": "207", "hashOfConfig": "111"}, {"size": 5619, "mtime": 1751190125112, "results": "208", "hashOfConfig": "111"}, {"size": 5823, "mtime": 1751190205858, "results": "209", "hashOfConfig": "111"}, {"size": 31995, "mtime": 1751217647037, "results": "210", "hashOfConfig": "111"}, {"size": 977, "mtime": 1749746063985, "results": "211", "hashOfConfig": "111"}, {"size": 8985, "mtime": 1751206912079, "results": "212", "hashOfConfig": "111"}, {"size": 7542, "mtime": 1751190440918, "results": "213", "hashOfConfig": "111"}, {"size": 7418, "mtime": 1751224969690, "results": "214", "hashOfConfig": "111"}, {"size": 4230, "mtime": 1751271092642, "results": "215", "hashOfConfig": "111"}, {"size": 16274, "mtime": 1751320792019, "results": "216", "hashOfConfig": "111"}, {"size": 12342, "mtime": 1751321443586, "results": "217", "hashOfConfig": "111"}, {"size": 7237, "mtime": 1751320287043, "results": "218", "hashOfConfig": "111"}, {"size": 14888, "mtime": 1751321538388, "results": "219", "hashOfConfig": "111"}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eamnk2", {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js", ["547"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js", ["548"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js", ["549", "550", "551"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js", ["552", "553"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js", ["554", "555", "556", "557", "558", "559", "560", "561", "562", "563"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js", ["564", "565", "566"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js", ["567", "568"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js", ["569", "570", "571", "572", "573"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js", ["574"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js", ["575", "576"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js", ["577"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js", ["578", "579"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js", ["580"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js", ["581", "582", "583", "584"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js", ["585", "586", "587", "588"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js", ["589"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js", ["590", "591", "592"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js", ["593"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js", ["594", "595"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js", ["596"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js", ["597", "598", "599", "600", "601"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js", ["602"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js", ["603", "604", "605"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js", ["606"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js", ["607", "608", "609", "610", "611"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js", ["612", "613", "614", "615", "616"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js", ["617"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js", ["618"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js", ["619"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js", ["620", "621"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js", ["622", "623", "624", "625"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js", ["626"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js", ["627", "628"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js", ["629", "630"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js", ["631", "632", "633"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js", ["634"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js", ["635", "636", "637"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js", ["638"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js", ["639", "640"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js", ["641", "642", "643", "644"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js", ["645", "646"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js", ["647", "648"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AuthLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js", ["649"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js", ["650", "651"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CurrencyManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencySelector.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencyAmount.js", ["652"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TestLogin.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\faviconUtils.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserSettings.js", ["653"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\User2FASetup.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserSettingsSimple.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserSettingsFixed.js", [], [], {"ruleId": "654", "severity": 1, "message": "655", "line": 73, "column": 8, "nodeType": "656", "messageId": "657", "endLine": 73, "endColumn": 23}, {"ruleId": "658", "severity": 1, "message": "659", "line": 31, "column": 6, "nodeType": "660", "endLine": 31, "endColumn": 8, "suggestions": "661"}, {"ruleId": "658", "severity": 1, "message": "662", "line": 27, "column": 9, "nodeType": "663", "endLine": 27, "endColumn": 62}, {"ruleId": "658", "severity": 1, "message": "664", "line": 75, "column": 6, "nodeType": "660", "endLine": 75, "endColumn": 50, "suggestions": "665"}, {"ruleId": "658", "severity": 1, "message": "666", "line": 135, "column": 6, "nodeType": "660", "endLine": 135, "endColumn": 103, "suggestions": "667"}, {"ruleId": "654", "severity": 1, "message": "668", "line": 4, "column": 121, "nodeType": "656", "messageId": "657", "endLine": 4, "endColumn": 132}, {"ruleId": "654", "severity": 1, "message": "669", "line": 11, "column": 11, "nodeType": "656", "messageId": "657", "endLine": 11, "endColumn": 19}, {"ruleId": "654", "severity": 1, "message": "670", "line": 8, "column": 3, "nodeType": "656", "messageId": "657", "endLine": 8, "endColumn": 16}, {"ruleId": "654", "severity": 1, "message": "671", "line": 10, "column": 3, "nodeType": "656", "messageId": "657", "endLine": 10, "endColumn": 17}, {"ruleId": "654", "severity": 1, "message": "672", "line": 11, "column": 3, "nodeType": "656", "messageId": "657", "endLine": 11, "endColumn": 13}, {"ruleId": "654", "severity": 1, "message": "673", "line": 12, "column": 3, "nodeType": "656", "messageId": "657", "endLine": 12, "endColumn": 16}, {"ruleId": "654", "severity": 1, "message": "674", "line": 13, "column": 3, "nodeType": "656", "messageId": "657", "endLine": 13, "endColumn": 8}, {"ruleId": "654", "severity": 1, "message": "675", "line": 14, "column": 3, "nodeType": "656", "messageId": "657", "endLine": 14, "endColumn": 16}, {"ruleId": "654", "severity": 1, "message": "676", "line": 15, "column": 3, "nodeType": "656", "messageId": "657", "endLine": 15, "endColumn": 10}, {"ruleId": "654", "severity": 1, "message": "677", "line": 16, "column": 3, "nodeType": "656", "messageId": "657", "endLine": 16, "endColumn": 9}, {"ruleId": "654", "severity": 1, "message": "678", "line": 20, "column": 16, "nodeType": "656", "messageId": "657", "endLine": 20, "endColumn": 19}, {"ruleId": "654", "severity": 1, "message": "679", "line": 47, "column": 12, "nodeType": "656", "messageId": "657", "endLine": 47, "endColumn": 30}, {"ruleId": "654", "severity": 1, "message": "680", "line": 5, "column": 23, "nodeType": "656", "messageId": "657", "endLine": 5, "endColumn": 38}, {"ruleId": "654", "severity": 1, "message": "681", "line": 10, "column": 7, "nodeType": "656", "messageId": "657", "endLine": 10, "endColumn": 19}, {"ruleId": "658", "severity": 1, "message": "682", "line": 33, "column": 8, "nodeType": "660", "endLine": 33, "endColumn": 10, "suggestions": "683"}, {"ruleId": "654", "severity": 1, "message": "684", "line": 20, "column": 10, "nodeType": "656", "messageId": "657", "endLine": 20, "endColumn": 20}, {"ruleId": "654", "severity": 1, "message": "685", "line": 20, "column": 22, "nodeType": "656", "messageId": "657", "endLine": 20, "endColumn": 35}, {"ruleId": "654", "severity": 1, "message": "686", "line": 3, "column": 20, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 28}, {"ruleId": "654", "severity": 1, "message": "687", "line": 3, "column": 37, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 43}, {"ruleId": "654", "severity": 1, "message": "688", "line": 3, "column": 45, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 52}, {"ruleId": "654", "severity": 1, "message": "689", "line": 13, "column": 21, "nodeType": "656", "messageId": "657", "endLine": 13, "endColumn": 31}, {"ruleId": "658", "severity": 1, "message": "690", "line": 40, "column": 8, "nodeType": "660", "endLine": 40, "endColumn": 41, "suggestions": "691"}, {"ruleId": "654", "severity": 1, "message": "692", "line": 17, "column": 12, "nodeType": "656", "messageId": "657", "endLine": 17, "endColumn": 21}, {"ruleId": "654", "severity": 1, "message": "686", "line": 3, "column": 20, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 28}, {"ruleId": "658", "severity": 1, "message": "693", "line": 39, "column": 8, "nodeType": "660", "endLine": 39, "endColumn": 42, "suggestions": "694"}, {"ruleId": "654", "severity": 1, "message": "695", "line": 3, "column": 34, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 41}, {"ruleId": "658", "severity": 1, "message": "696", "line": 23, "column": 8, "nodeType": "660", "endLine": 23, "endColumn": 19, "suggestions": "697"}, {"ruleId": "658", "severity": 1, "message": "698", "line": 32, "column": 8, "nodeType": "660", "endLine": 32, "endColumn": 30, "suggestions": "699"}, {"ruleId": "658", "severity": 1, "message": "700", "line": 43, "column": 8, "nodeType": "660", "endLine": 43, "endColumn": 10, "suggestions": "701"}, {"ruleId": "654", "severity": 1, "message": "702", "line": 11, "column": 5, "nodeType": "656", "messageId": "657", "endLine": 11, "endColumn": 20}, {"ruleId": "654", "severity": 1, "message": "703", "line": 16, "column": 5, "nodeType": "656", "messageId": "657", "endLine": 16, "endColumn": 12}, {"ruleId": "654", "severity": 1, "message": "704", "line": 17, "column": 5, "nodeType": "656", "messageId": "657", "endLine": 17, "endColumn": 17}, {"ruleId": "654", "severity": 1, "message": "695", "line": 18, "column": 5, "nodeType": "656", "messageId": "657", "endLine": 18, "endColumn": 12}, {"ruleId": "654", "severity": 1, "message": "705", "line": 1, "column": 60, "nodeType": "656", "messageId": "657", "endLine": 1, "endColumn": 66}, {"ruleId": "654", "severity": 1, "message": "706", "line": 28, "column": 12, "nodeType": "656", "messageId": "657", "endLine": 28, "endColumn": 26}, {"ruleId": "654", "severity": 1, "message": "707", "line": 28, "column": 28, "nodeType": "656", "messageId": "657", "endLine": 28, "endColumn": 45}, {"ruleId": "658", "severity": 1, "message": "708", "line": 83, "column": 8, "nodeType": "660", "endLine": 83, "endColumn": 10, "suggestions": "709"}, {"ruleId": "654", "severity": 1, "message": "710", "line": 4, "column": 10, "nodeType": "656", "messageId": "657", "endLine": 4, "endColumn": 22}, {"ruleId": "654", "severity": 1, "message": "668", "line": 3, "column": 19, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 30}, {"ruleId": "654", "severity": 1, "message": "711", "line": 14, "column": 12, "nodeType": "656", "messageId": "657", "endLine": 14, "endColumn": 24}, {"ruleId": "654", "severity": 1, "message": "712", "line": 93, "column": 11, "nodeType": "656", "messageId": "657", "endLine": 93, "endColumn": 23}, {"ruleId": "658", "severity": 1, "message": "713", "line": 84, "column": 8, "nodeType": "660", "endLine": 84, "endColumn": 18, "suggestions": "714"}, {"ruleId": "654", "severity": 1, "message": "715", "line": 113, "column": 9, "nodeType": "656", "messageId": "657", "endLine": 113, "endColumn": 25}, {"ruleId": "654", "severity": 1, "message": "716", "line": 147, "column": 9, "nodeType": "656", "messageId": "657", "endLine": 147, "endColumn": 22}, {"ruleId": "654", "severity": 1, "message": "717", "line": 4, "column": 10, "nodeType": "656", "messageId": "657", "endLine": 4, "endColumn": 17}, {"ruleId": "654", "severity": 1, "message": "718", "line": 13, "column": 10, "nodeType": "656", "messageId": "657", "endLine": 13, "endColumn": 17}, {"ruleId": "654", "severity": 1, "message": "719", "line": 14, "column": 10, "nodeType": "656", "messageId": "657", "endLine": 14, "endColumn": 15}, {"ruleId": "654", "severity": 1, "message": "716", "line": 115, "column": 9, "nodeType": "656", "messageId": "657", "endLine": 115, "endColumn": 22}, {"ruleId": "654", "severity": 1, "message": "720", "line": 132, "column": 9, "nodeType": "656", "messageId": "657", "endLine": 132, "endColumn": 19}, {"ruleId": "654", "severity": 1, "message": "721", "line": 145, "column": 9, "nodeType": "656", "messageId": "657", "endLine": 145, "endColumn": 22}, {"ruleId": "658", "severity": 1, "message": "693", "line": 44, "column": 8, "nodeType": "660", "endLine": 44, "endColumn": 21, "suggestions": "722"}, {"ruleId": "654", "severity": 1, "message": "723", "line": 10, "column": 10, "nodeType": "656", "messageId": "657", "endLine": 10, "endColumn": 23}, {"ruleId": "654", "severity": 1, "message": "724", "line": 10, "column": 25, "nodeType": "656", "messageId": "657", "endLine": 10, "endColumn": 41}, {"ruleId": "654", "severity": 1, "message": "725", "line": 18, "column": 25, "nodeType": "656", "messageId": "657", "endLine": 18, "endColumn": 46}, {"ruleId": "658", "severity": 1, "message": "726", "line": 19, "column": 8, "nodeType": "660", "endLine": 19, "endColumn": 10, "suggestions": "727"}, {"ruleId": "654", "severity": 1, "message": "710", "line": 4, "column": 10, "nodeType": "656", "messageId": "657", "endLine": 4, "endColumn": 22}, {"ruleId": "654", "severity": 1, "message": "728", "line": 7, "column": 14, "nodeType": "656", "messageId": "657", "endLine": 7, "endColumn": 20}, {"ruleId": "654", "severity": 1, "message": "704", "line": 7, "column": 41, "nodeType": "656", "messageId": "657", "endLine": 7, "endColumn": 53}, {"ruleId": "654", "severity": 1, "message": "729", "line": 8, "column": 46, "nodeType": "656", "messageId": "657", "endLine": 8, "endColumn": 52}, {"ruleId": "654", "severity": 1, "message": "730", "line": 306, "column": 11, "nodeType": "656", "messageId": "657", "endLine": 306, "endColumn": 27}, {"ruleId": "654", "severity": 1, "message": "731", "line": 4, "column": 45, "nodeType": "656", "messageId": "657", "endLine": 4, "endColumn": 64}, {"ruleId": "654", "severity": 1, "message": "732", "line": 4, "column": 66, "nodeType": "656", "messageId": "657", "endLine": 4, "endColumn": 79}, {"ruleId": "654", "severity": 1, "message": "733", "line": 4, "column": 111, "nodeType": "656", "messageId": "657", "endLine": 4, "endColumn": 123}, {"ruleId": "658", "severity": 1, "message": "734", "line": 29, "column": 8, "nodeType": "660", "endLine": 29, "endColumn": 10, "suggestions": "735"}, {"ruleId": "654", "severity": 1, "message": "736", "line": 256, "column": 11, "nodeType": "656", "messageId": "657", "endLine": 256, "endColumn": 26}, {"ruleId": "658", "severity": 1, "message": "737", "line": 24, "column": 8, "nodeType": "660", "endLine": 24, "endColumn": 33, "suggestions": "738"}, {"ruleId": "654", "severity": 1, "message": "728", "line": 5, "column": 57, "nodeType": "656", "messageId": "657", "endLine": 5, "endColumn": 63}, {"ruleId": "658", "severity": 1, "message": "739", "line": 24, "column": 8, "nodeType": "660", "endLine": 24, "endColumn": 33, "suggestions": "740"}, {"ruleId": "654", "severity": 1, "message": "741", "line": 121, "column": 19, "nodeType": "656", "messageId": "657", "endLine": 121, "endColumn": 28}, {"ruleId": "654", "severity": 1, "message": "742", "line": 136, "column": 19, "nodeType": "656", "messageId": "657", "endLine": 136, "endColumn": 22}, {"ruleId": "654", "severity": 1, "message": "743", "line": 4, "column": 5, "nodeType": "656", "messageId": "657", "endLine": 4, "endColumn": 14}, {"ruleId": "654", "severity": 1, "message": "744", "line": 6, "column": 5, "nodeType": "656", "messageId": "657", "endLine": 6, "endColumn": 10}, {"ruleId": "654", "severity": 1, "message": "672", "line": 7, "column": 5, "nodeType": "656", "messageId": "657", "endLine": 7, "endColumn": 15}, {"ruleId": "654", "severity": 1, "message": "745", "line": 8, "column": 5, "nodeType": "656", "messageId": "657", "endLine": 8, "endColumn": 16}, {"ruleId": "654", "severity": 1, "message": "746", "line": 57, "column": 9, "nodeType": "656", "messageId": "657", "endLine": 57, "endColumn": 26}, {"ruleId": "654", "severity": 1, "message": "671", "line": 2, "column": 10, "nodeType": "656", "messageId": "657", "endLine": 2, "endColumn": 24}, {"ruleId": "654", "severity": 1, "message": "747", "line": 2, "column": 26, "nodeType": "656", "messageId": "657", "endLine": 2, "endColumn": 34}, {"ruleId": "654", "severity": 1, "message": "748", "line": 3, "column": 35, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 41}, {"ruleId": "654", "severity": 1, "message": "749", "line": 5, "column": 25, "nodeType": "656", "messageId": "657", "endLine": 5, "endColumn": 39}, {"ruleId": "654", "severity": 1, "message": "750", "line": 5, "column": 5, "nodeType": "656", "messageId": "657", "endLine": 5, "endColumn": 15}, {"ruleId": "654", "severity": 1, "message": "670", "line": 6, "column": 5, "nodeType": "656", "messageId": "657", "endLine": 6, "endColumn": 18}, {"ruleId": "658", "severity": 1, "message": "751", "line": 29, "column": 8, "nodeType": "660", "endLine": 29, "endColumn": 31, "suggestions": "752"}, {"ruleId": "658", "severity": 1, "message": "693", "line": 31, "column": 8, "nodeType": "660", "endLine": 31, "endColumn": 17, "suggestions": "753"}, {"ruleId": "654", "severity": 1, "message": "754", "line": 3, "column": 33, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 38}, {"ruleId": "654", "severity": 1, "message": "755", "line": 14, "column": 12, "nodeType": "656", "messageId": "657", "endLine": 14, "endColumn": 21}, {"ruleId": "658", "severity": 1, "message": "756", "line": 29, "column": 8, "nodeType": "660", "endLine": 29, "endColumn": 10, "suggestions": "757"}, {"ruleId": "654", "severity": 1, "message": "758", "line": 3, "column": 41, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 62}, {"ruleId": "654", "severity": 1, "message": "758", "line": 3, "column": 40, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 61}, {"ruleId": "658", "severity": 1, "message": "759", "line": 28, "column": 8, "nodeType": "660", "endLine": 28, "endColumn": 24, "suggestions": "760"}, {"ruleId": "654", "severity": 1, "message": "695", "line": 3, "column": 51, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 58}, {"ruleId": "654", "severity": 1, "message": "744", "line": 3, "column": 102, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 107}, {"ruleId": "654", "severity": 1, "message": "761", "line": 18, "column": 12, "nodeType": "656", "messageId": "657", "endLine": 18, "endColumn": 21}, {"ruleId": "658", "severity": 1, "message": "762", "line": 23, "column": 8, "nodeType": "660", "endLine": 23, "endColumn": 17, "suggestions": "763"}, {"ruleId": "654", "severity": 1, "message": "764", "line": 3, "column": 40, "nodeType": "656", "messageId": "657", "endLine": 3, "endColumn": 46}, {"ruleId": "658", "severity": 1, "message": "765", "line": 30, "column": 8, "nodeType": "660", "endLine": 30, "endColumn": 10, "suggestions": "766"}, {"ruleId": "654", "severity": 1, "message": "767", "line": 6, "column": 5, "nodeType": "656", "messageId": "657", "endLine": 6, "endColumn": 11}, {"ruleId": "658", "severity": 1, "message": "768", "line": 38, "column": 8, "nodeType": "660", "endLine": 38, "endColumn": 16, "suggestions": "769"}, {"ruleId": "658", "severity": 1, "message": "770", "line": 51, "column": 8, "nodeType": "660", "endLine": 51, "endColumn": 10, "suggestions": "771"}, {"ruleId": "772", "severity": 1, "message": "773", "line": 93, "column": 39, "nodeType": "774", "messageId": "775", "endLine": 93, "endColumn": 86}, {"ruleId": "654", "severity": 1, "message": "776", "line": 177, "column": 15, "nodeType": "656", "messageId": "657", "endLine": 177, "endColumn": 30}, {"ruleId": "654", "severity": 1, "message": "777", "line": 144, "column": 36, "nodeType": "656", "messageId": "657", "endLine": 144, "endColumn": 48}, {"ruleId": null, "fatal": true, "severity": 2, "message": "778", "line": 386, "column": 8, "nodeType": null}, "no-unused-vars", "'LeagueSelection' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'removeError'. Either include it or remove the dependency array.", "ArrayExpression", ["779"], "The 'publicRoutes' array makes the dependencies of useCallback Hook (at line 110) change on every render. To fix this, wrap the initialization of 'publicRoutes' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'setUserData'. Either include it or remove the dependency array.", ["780"], "React Hook useEffect has a missing dependency: 'publicRoutes'. Either include it or remove the dependency array.", ["781"], "'FaChartLine' is defined but never used.", "'navigate' is assigned a value but never used.", "'FaCalendarAlt' is defined but never used.", "'FaFootballBall' is defined but never used.", "'FaChartBar' is defined but never used.", "'FaExchangeAlt' is defined but never used.", "'FaEye' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'FaClock' is defined but never used.", "'FaBell' is defined but never used.", "'Bar' is defined but never used.", "'teamPopularityData' is assigned a value but never used.", "'currencyService' is defined but never used.", "'API_BASE_URL' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTeams'. Either include it or remove the dependency array.", ["782"], "'challenges' is assigned a value but never used.", "'setChallenges' is assigned a value but never used.", "'FaFilter' is defined but never used.", "'FaEdit' is defined but never used.", "'FaTrash' is defined but never used.", "'setSuccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllBets'. Either include it or remove the dependency array.", ["783"], "'otpExpiry' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLeaderboard'. Either include it or remove the dependency array.", ["784"], "'FaTimes' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchConversations'. Either include it or remove the dependency array.", ["785"], "React Hook useEffect has missing dependencies: 'fetchMessages' and 'markMessagesAsRead'. Either include them or remove the dependency array.", ["786"], "React Hook useEffect has a missing dependency: 'checkChallengeStatus'. Either include it or remove the dependency array.", ["787"], "'FaMoneyBillWave' is defined but never used.", "'FaUsers' is defined but never used.", "'FaInfoCircle' is defined but never used.", "'useRef' is defined but never used.", "'payoutPreviews' is assigned a value but never used.", "'setPayoutPreviews' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'fetchLeagueDetails'. Either include it or remove the dependency array.", ["788"], "'API_BASE_URL' is defined but never used.", "'loadingUsers' is assigned a value but never used.", "'currentUsers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserData'. Either include it or remove the dependency array.", ["789"], "'renderPagination' is assigned a value but never used.", "'calculateOdds' is assigned a value but never used.", "'FaCoins' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'formatDate' is assigned a value but never used.", "'getUserStatus' is assigned a value but never used.", ["790"], "'paymentMethod' is assigned a value but never used.", "'setPaymentMethod' is assigned a value but never used.", "'convertToUserCurrency' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPendingRequests'. Either include it or remove the dependency array.", ["791"], "'FaStar' is defined but never used.", "'FaFire' is defined but never used.", "'renderNavigation' is assigned a value but never used.", "'FaExclamationCircle' is defined but never used.", "'FaUserFriends' is defined but never used.", "'FaUserCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFriends'. Either include it or remove the dependency array.", ["792"], "'handleChallenge' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["793"], "React Hook useEffect has a missing dependency: 'fetchCreditRequests'. Either include it or remove the dependency array.", ["794"], "'matchDate' is assigned a value but never used.", "'now' is assigned a value but never used.", "'FaGamepad' is defined but never used.", "'FaCog' is defined but never used.", "'FaMoneyBill' is defined but never used.", "'handleLogoutClick' is assigned a value but never used.", "'FaTrophy' is defined but never used.", "'FaSave' is defined but never used.", "'refreshFavicon' is defined but never used.", "'FaDownload' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReport'. Either include it or remove the dependency array.", ["795"], ["796"], "'FaKey' is defined but never used.", "'secretKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initiate2FASetup'. Either include it or remove the dependency array.", ["797"], "'FaExclamationTriangle' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendInitialOTP'. Either include it or remove the dependency array.", ["798"], "'adminInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPreferences'. Either include it or remove the dependency array.", ["799"], "'FaCopy' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["800"], "'FaUser' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchUserBets', 'fetchUserDetails', and 'fetchUserTransactions'. Either include them or remove the dependency array.", ["801"], "React Hook useCallback has a missing dependency: 'CACHE_DURATION'. Either include it or remove the dependency array.", ["802"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'attempt'.", "ArrowFunctionExpression", "unsafeRefs", "'originalExecute' is assigned a value but never used.", "'userCurrency' is assigned a value but never used.", "Parsing error: Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>? (386:8)", {"desc": "803", "fix": "804"}, {"desc": "805", "fix": "806"}, {"desc": "807", "fix": "808"}, {"desc": "809", "fix": "810"}, {"desc": "811", "fix": "812"}, {"desc": "813", "fix": "814"}, {"desc": "815", "fix": "816"}, {"desc": "817", "fix": "818"}, {"desc": "819", "fix": "820"}, {"desc": "821", "fix": "822"}, {"desc": "823", "fix": "824"}, {"desc": "825", "fix": "826"}, {"desc": "827", "fix": "828"}, {"desc": "829", "fix": "830"}, {"desc": "831", "fix": "832"}, {"desc": "833", "fix": "834"}, {"desc": "835", "fix": "836"}, {"desc": "837", "fix": "838"}, {"desc": "839", "fix": "840"}, {"desc": "841", "fix": "842"}, {"desc": "843", "fix": "844"}, {"desc": "845", "fix": "846"}, {"desc": "847", "fix": "848"}, {"desc": "849", "fix": "850"}, "Update the dependencies array to be: [removeError]", {"range": "851", "text": "852"}, "Update the dependencies array to be: [token, userId, setUserData, navigate, location.pathname]", {"range": "853", "text": "854"}, "Update the dependencies array to be: [token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, fetchSidebarLogo, publicRoutes]", {"range": "855", "text": "856"}, "Update the dependencies array to be: [fetchTeams]", {"range": "857", "text": "858"}, "Update the dependencies array to be: [pagination.currentPage, filters, fetchAllBets]", {"range": "859", "text": "860"}, "Update the dependencies array to be: [pagination.current_page, filters, fetchLeaderboard]", {"range": "861", "text": "862"}, "Update the dependencies array to be: [activeTab, fetchConversations]", {"range": "863", "text": "864"}, "Update the dependencies array to be: [fetchMessages, markMessagesAsRead, selectedConversation]", {"range": "865", "text": "866"}, "Update the dependencies array to be: [checkChallengeStatus]", {"range": "867", "text": "868"}, "Update the dependencies array to be: [fetchLeagueDetails]", {"range": "869", "text": "870"}, "Update the dependencies array to be: [fetchUserData, navigate]", {"range": "871", "text": "872"}, "Update the dependencies array to be: [currentPage, fetchLeaderboard]", {"range": "873", "text": "874"}, "Update the dependencies array to be: [fetchPendingRequests]", {"range": "875", "text": "876"}, "Update the dependencies array to be: [fetchFriends]", {"range": "877", "text": "878"}, "Update the dependencies array to be: [navigate, currentUserId, fetchInitialData]", {"range": "879", "text": "880"}, "Update the dependencies array to be: [navigate, currentUserId, fetchCreditRequests]", {"range": "881", "text": "882"}, "Update the dependencies array to be: [reportType, dateRange, fetchReport]", {"range": "883", "text": "884"}, "Update the dependencies array to be: [fetchLeaderboard, filters]", {"range": "885", "text": "886"}, "Update the dependencies array to be: [initiate2FASetup]", {"range": "887", "text": "888"}, "Update the dependencies array to be: [initialOtpSent, sendInitialOTP]", {"range": "889", "text": "890"}, "Update the dependencies array to be: [adminId, fetchPreferences]", {"range": "891", "text": "892"}, "Update the dependencies array to be: [fetchAdminData]", {"range": "893", "text": "894"}, "Update the dependencies array to be: [fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", {"range": "895", "text": "896"}, "Update the dependencies array to be: [CACHE_DURATION]", {"range": "897", "text": "898"}, [985, 987], "[removeError]", [2754, 2798], "[token, userId, setUserData, navigate, location.pathname]", [4647, 4744], "[token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, fetchSidebarLogo, publicRoutes]", [1143, 1145], "[fetchTeams]", [1193, 1226], "[pagination.currentPage, filters, fetchAllBets]", [1249, 1283], "[pagination.current_page, filters, fetchLeaderboard]", [1071, 1082], "[activeTab, fetchConversations]", [1429, 1451], "[fetch<PERSON>essages, markMessagesAsRead, selectedConversation]", [1532, 1534], "[checkChallengeStatus]", [3005, 3007], "[fetchLeagueDetails]", [3404, 3414], "[fetch<PERSON><PERSON><PERSON><PERSON>, navigate]", [1160, 1173], "[currentPage, fetchLeaderboard]", [696, 698], "[fetchPendingRequests]", [1331, 1333], "[fetchFriends]", [856, 881], "[navigate, currentUserId, fetchInitialData]", [891, 916], "[navigate, currentUserId, fetchCreditRequests]", [864, 887], "[reportType, dateRange, fetchReport]", [887, 896], "[fetchLeaderboard, filters]", [1137, 1139], "[initiate2FASetup]", [1264, 1280], "[initialOtpSent, sendInitialOTP]", [856, 865], "[adminId, fetchPreferences]", [1269, 1271], "[fetchAdminData]", [1036, 1044], "[fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", [1873, 1875], "[CACHE_DURATION]"]