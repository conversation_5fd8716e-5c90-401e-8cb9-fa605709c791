[{"C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js": "1", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js": "2", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js": "3", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js": "4", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js": "5", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js": "6", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js": "7", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js": "8", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js": "9", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js": "10", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js": "11", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js": "12", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js": "13", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js": "14", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js": "15", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js": "16", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js": "17", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js": "18", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js": "19", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js": "20", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js": "21", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js": "22", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js": "23", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js": "24", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js": "25", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js": "26", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js": "27", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js": "28", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js": "29", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js": "30", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js": "31", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js": "32", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js": "33", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js": "34", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js": "35", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js": "36", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js": "37", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js": "38", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js": "39", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js": "40", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js": "41", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js": "42", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js": "43", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js": "44", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js": "45", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js": "46", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js": "47", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js": "48", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js": "49", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js": "50", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js": "51", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js": "52", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js": "53", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js": "54", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js": "55", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js": "56", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js": "57", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js": "58", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js": "59", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js": "60", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js": "61", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js": "62", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js": "63", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js": "64", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js": "65", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js": "66", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js": "67", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js": "68", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js": "69", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js": "70", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js": "71", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js": "72", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js": "73", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js": "74", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js": "75", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js": "76", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js": "77", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js": "78", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js": "79", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js": "80", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js": "81", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js": "82", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js": "83", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js": "84", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js": "85", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js": "86", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js": "87", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js": "88", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js": "89", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AuthLayout.js": "90", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js": "91", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js": "92", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js": "93", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js": "94", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js": "95", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js": "96", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js": "97", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js": "98", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js": "99", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CurrencyManagement.js": "100", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\index.js": "101", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencySelector.js": "102", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencyAmount.js": "103", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TestLogin.js": "104", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\faviconUtils.js": "105", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserSettings.js": "106", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\User2FASetup.js": "107"}, {"size": 1593, "mtime": 1739215325917, "results": "108", "hashOfConfig": "109"}, {"size": 12096, "mtime": 1751319051541, "results": "110", "hashOfConfig": "109"}, {"size": 362, "mtime": 1725527312699, "results": "111", "hashOfConfig": "109"}, {"size": 3418, "mtime": 1751289023276, "results": "112", "hashOfConfig": "109"}, {"size": 1583, "mtime": 1738380484748, "results": "113", "hashOfConfig": "109"}, {"size": 1958, "mtime": 1738907546846, "results": "114", "hashOfConfig": "109"}, {"size": 1431, "mtime": 1747472215947, "results": "115", "hashOfConfig": "109"}, {"size": 18387, "mtime": 1751314962308, "results": "116", "hashOfConfig": "109"}, {"size": 5798, "mtime": 1751306691860, "results": "117", "hashOfConfig": "109"}, {"size": 10946, "mtime": 1749736717528, "results": "118", "hashOfConfig": "109"}, {"size": 63994, "mtime": 1751297486890, "results": "119", "hashOfConfig": "109"}, {"size": 34669, "mtime": 1749746085318, "results": "120", "hashOfConfig": "109"}, {"size": 10808, "mtime": 1751196036202, "results": "121", "hashOfConfig": "109"}, {"size": 18492, "mtime": 1751297568375, "results": "122", "hashOfConfig": "109"}, {"size": 25373, "mtime": 1751220003165, "results": "123", "hashOfConfig": "109"}, {"size": 41023, "mtime": 1749746085319, "results": "124", "hashOfConfig": "109"}, {"size": 16711, "mtime": 1751220332449, "results": "125", "hashOfConfig": "109"}, {"size": 43308, "mtime": 1749118478833, "results": "126", "hashOfConfig": "109"}, {"size": 14068, "mtime": 1751297498169, "results": "127", "hashOfConfig": "109"}, {"size": 484, "mtime": 1747774257496, "results": "128", "hashOfConfig": "109"}, {"size": 3867, "mtime": 1749105548034, "results": "129", "hashOfConfig": "109"}, {"size": 19635, "mtime": 1751297527768, "results": "130", "hashOfConfig": "109"}, {"size": 15481, "mtime": 1751290367000, "results": "131", "hashOfConfig": "109"}, {"size": 6470, "mtime": 1751306633077, "results": "132", "hashOfConfig": "109"}, {"size": 398, "mtime": 1725625029363, "results": "133", "hashOfConfig": "109"}, {"size": 10599, "mtime": 1749746085318, "results": "134", "hashOfConfig": "109"}, {"size": 30545, "mtime": 1751297583377, "results": "135", "hashOfConfig": "109"}, {"size": 37419, "mtime": 1751297458472, "results": "136", "hashOfConfig": "109"}, {"size": 22302, "mtime": 1751297597725, "results": "137", "hashOfConfig": "109"}, {"size": 9112, "mtime": 1734251655762, "results": "138", "hashOfConfig": "109"}, {"size": 11975, "mtime": 1749736717528, "results": "139", "hashOfConfig": "109"}, {"size": 13002, "mtime": 1751295464370, "results": "140", "hashOfConfig": "109"}, {"size": 12046, "mtime": 1751297683810, "results": "141", "hashOfConfig": "109"}, {"size": 15644, "mtime": 1751220144047, "results": "142", "hashOfConfig": "109"}, {"size": 18020, "mtime": 1751314931751, "results": "143", "hashOfConfig": "109"}, {"size": 12891, "mtime": 1749736717512, "results": "144", "hashOfConfig": "109"}, {"size": 29744, "mtime": 1751306674927, "results": "145", "hashOfConfig": "109"}, {"size": 5321, "mtime": 1751306646939, "results": "146", "hashOfConfig": "109"}, {"size": 205, "mtime": 1732832805260, "results": "147", "hashOfConfig": "109"}, {"size": 28050, "mtime": 1738011980316, "results": "148", "hashOfConfig": "109"}, {"size": 31265, "mtime": 1751290174162, "results": "149", "hashOfConfig": "109"}, {"size": 8917, "mtime": 1738228976181, "results": "150", "hashOfConfig": "109"}, {"size": 1242, "mtime": 1732832820214, "results": "151", "hashOfConfig": "109"}, {"size": 8290, "mtime": 1751193168091, "results": "152", "hashOfConfig": "109"}, {"size": 1098, "mtime": 1732832839965, "results": "153", "hashOfConfig": "109"}, {"size": 11530, "mtime": 1732983571250, "results": "154", "hashOfConfig": "109"}, {"size": 23387, "mtime": 1751295428183, "results": "155", "hashOfConfig": "109"}, {"size": 24442, "mtime": 1751297418539, "results": "156", "hashOfConfig": "109"}, {"size": 4310, "mtime": 1734245942035, "results": "157", "hashOfConfig": "109"}, {"size": 5623, "mtime": 1734245958195, "results": "158", "hashOfConfig": "109"}, {"size": 3339, "mtime": 1734245925091, "results": "159", "hashOfConfig": "109"}, {"size": 6576, "mtime": 1751295326646, "results": "160", "hashOfConfig": "109"}, {"size": 5681, "mtime": 1734287339563, "results": "161", "hashOfConfig": "109"}, {"size": 10920, "mtime": 1739168463615, "results": "162", "hashOfConfig": "109"}, {"size": 14257, "mtime": 1739212427178, "results": "163", "hashOfConfig": "109"}, {"size": 16886, "mtime": 1751297275313, "results": "164", "hashOfConfig": "109"}, {"size": 21457, "mtime": 1751289936798, "results": "165", "hashOfConfig": "109"}, {"size": 3211, "mtime": 1747478622718, "results": "166", "hashOfConfig": "109"}, {"size": 7496, "mtime": 1751193097200, "results": "167", "hashOfConfig": "109"}, {"size": 1352, "mtime": 1738907631772, "results": "168", "hashOfConfig": "109"}, {"size": 591, "mtime": 1737714035353, "results": "169", "hashOfConfig": "109"}, {"size": 4889, "mtime": 1739089917990, "results": "170", "hashOfConfig": "109"}, {"size": 4026, "mtime": 1749114060143, "results": "171", "hashOfConfig": "109"}, {"size": 2690, "mtime": 1749742809442, "results": "172", "hashOfConfig": "109"}, {"size": 597, "mtime": 1738005020143, "results": "173", "hashOfConfig": "109"}, {"size": 2649, "mtime": 1745558530865, "results": "174", "hashOfConfig": "109"}, {"size": 856, "mtime": 1738005002533, "results": "175", "hashOfConfig": "109"}, {"size": 778, "mtime": 1737703033090, "results": "176", "hashOfConfig": "109"}, {"size": 9268, "mtime": 1739089382382, "results": "177", "hashOfConfig": "109"}, {"size": 4473, "mtime": 1749736717521, "results": "178", "hashOfConfig": "109"}, {"size": 6511, "mtime": 1747772230646, "results": "179", "hashOfConfig": "109"}, {"size": 3561, "mtime": 1747465926259, "results": "180", "hashOfConfig": "109"}, {"size": 2058, "mtime": 1745560016985, "results": "181", "hashOfConfig": "109"}, {"size": 3270, "mtime": 1747683592095, "results": "182", "hashOfConfig": "109"}, {"size": 21913, "mtime": 1751279289062, "results": "183", "hashOfConfig": "109"}, {"size": 35666, "mtime": 1749736717528, "results": "184", "hashOfConfig": "109"}, {"size": 17158, "mtime": 1749113919875, "results": "185", "hashOfConfig": "109"}, {"size": 12332, "mtime": 1749106493490, "results": "186", "hashOfConfig": "109"}, {"size": 2047, "mtime": 1751271203937, "results": "187", "hashOfConfig": "109"}, {"size": 35110, "mtime": 1749118685592, "results": "188", "hashOfConfig": "109"}, {"size": 17866, "mtime": 1751287911119, "results": "189", "hashOfConfig": "109"}, {"size": 317, "mtime": 1749231241721, "results": "190", "hashOfConfig": "109"}, {"size": 22414, "mtime": 1749237247953, "results": "191", "hashOfConfig": "109"}, {"size": 15311, "mtime": 1749239054836, "results": "192", "hashOfConfig": "109"}, {"size": 19477, "mtime": 1749236876615, "results": "193", "hashOfConfig": "109"}, {"size": 12103, "mtime": 1749736717523, "results": "194", "hashOfConfig": "109"}, {"size": 26689, "mtime": 1749237355112, "results": "195", "hashOfConfig": "109"}, {"size": 16808, "mtime": 1751306663144, "results": "196", "hashOfConfig": "109"}, {"size": 3567, "mtime": 1749282608572, "results": "197", "hashOfConfig": "109"}, {"size": 3229, "mtime": 1749749514923, "results": "198", "hashOfConfig": "109"}, {"size": 13936, "mtime": 1749750686297, "results": "199", "hashOfConfig": "109"}, {"size": 1833, "mtime": 1749750397933, "results": "200", "hashOfConfig": "109"}, {"size": 9434, "mtime": 1751190331897, "results": "201", "hashOfConfig": "109"}, {"size": 3176, "mtime": 1751190174797, "results": "202", "hashOfConfig": "109"}, {"size": 5569, "mtime": 1751190153517, "results": "203", "hashOfConfig": "109"}, {"size": 4644, "mtime": 1751190097589, "results": "204", "hashOfConfig": "109"}, {"size": 6877, "mtime": 1751190061103, "results": "205", "hashOfConfig": "109"}, {"size": 5619, "mtime": 1751190125112, "results": "206", "hashOfConfig": "109"}, {"size": 5823, "mtime": 1751190205858, "results": "207", "hashOfConfig": "109"}, {"size": 31995, "mtime": 1751217647037, "results": "208", "hashOfConfig": "109"}, {"size": 977, "mtime": 1749746063985, "results": "209", "hashOfConfig": "109"}, {"size": 8985, "mtime": 1751206912079, "results": "210", "hashOfConfig": "109"}, {"size": 7542, "mtime": 1751190440918, "results": "211", "hashOfConfig": "109"}, {"size": 7418, "mtime": 1751224969690, "results": "212", "hashOfConfig": "109"}, {"size": 4230, "mtime": 1751271092642, "results": "213", "hashOfConfig": "109"}, {"size": 16540, "mtime": 1751318667723, "results": "214", "hashOfConfig": "109"}, {"size": 12546, "mtime": 1751318757530, "results": "215", "hashOfConfig": "109"}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eamnk2", {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js", ["537"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js", ["538"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js", ["539", "540", "541"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js", ["542", "543"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js", ["544", "545", "546", "547", "548", "549", "550", "551", "552", "553"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js", ["554", "555", "556"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js", ["557", "558"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js", ["559", "560", "561", "562", "563"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js", ["564"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js", ["565", "566"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js", ["567"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js", ["568", "569"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js", ["570"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js", ["571", "572", "573", "574"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js", ["575", "576", "577", "578"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js", ["579"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js", ["580", "581", "582"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js", ["583"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js", ["584", "585"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js", ["586"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js", ["587", "588", "589", "590", "591"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js", ["592"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js", ["593", "594", "595"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js", ["596"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js", ["597", "598", "599", "600", "601"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js", ["602", "603", "604", "605", "606"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js", ["607"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js", ["608"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js", ["609"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js", ["610", "611"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js", ["612", "613", "614", "615"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js", ["616"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js", ["617", "618"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js", ["619", "620"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js", ["621", "622", "623"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js", ["624"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js", ["625", "626", "627"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js", ["628"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js", ["629", "630"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js", ["631", "632", "633", "634"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js", ["635", "636"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js", ["637", "638"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AuthLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js", ["639"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js", ["640", "641"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CurrencyManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencySelector.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencyAmount.js", ["642"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TestLogin.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\faviconUtils.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserSettings.js", ["643"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\User\\User2FASetup.js", ["644", "645"], [], {"ruleId": "646", "severity": 1, "message": "647", "line": 73, "column": 8, "nodeType": "648", "messageId": "649", "endLine": 73, "endColumn": 23}, {"ruleId": "650", "severity": 1, "message": "651", "line": 31, "column": 6, "nodeType": "652", "endLine": 31, "endColumn": 8, "suggestions": "653"}, {"ruleId": "650", "severity": 1, "message": "654", "line": 27, "column": 9, "nodeType": "655", "endLine": 27, "endColumn": 62}, {"ruleId": "650", "severity": 1, "message": "656", "line": 75, "column": 6, "nodeType": "652", "endLine": 75, "endColumn": 50, "suggestions": "657"}, {"ruleId": "650", "severity": 1, "message": "658", "line": 135, "column": 6, "nodeType": "652", "endLine": 135, "endColumn": 103, "suggestions": "659"}, {"ruleId": "646", "severity": 1, "message": "660", "line": 4, "column": 121, "nodeType": "648", "messageId": "649", "endLine": 4, "endColumn": 132}, {"ruleId": "646", "severity": 1, "message": "661", "line": 11, "column": 11, "nodeType": "648", "messageId": "649", "endLine": 11, "endColumn": 19}, {"ruleId": "646", "severity": 1, "message": "662", "line": 8, "column": 3, "nodeType": "648", "messageId": "649", "endLine": 8, "endColumn": 16}, {"ruleId": "646", "severity": 1, "message": "663", "line": 10, "column": 3, "nodeType": "648", "messageId": "649", "endLine": 10, "endColumn": 17}, {"ruleId": "646", "severity": 1, "message": "664", "line": 11, "column": 3, "nodeType": "648", "messageId": "649", "endLine": 11, "endColumn": 13}, {"ruleId": "646", "severity": 1, "message": "665", "line": 12, "column": 3, "nodeType": "648", "messageId": "649", "endLine": 12, "endColumn": 16}, {"ruleId": "646", "severity": 1, "message": "666", "line": 13, "column": 3, "nodeType": "648", "messageId": "649", "endLine": 13, "endColumn": 8}, {"ruleId": "646", "severity": 1, "message": "667", "line": 14, "column": 3, "nodeType": "648", "messageId": "649", "endLine": 14, "endColumn": 16}, {"ruleId": "646", "severity": 1, "message": "668", "line": 15, "column": 3, "nodeType": "648", "messageId": "649", "endLine": 15, "endColumn": 10}, {"ruleId": "646", "severity": 1, "message": "669", "line": 16, "column": 3, "nodeType": "648", "messageId": "649", "endLine": 16, "endColumn": 9}, {"ruleId": "646", "severity": 1, "message": "670", "line": 20, "column": 16, "nodeType": "648", "messageId": "649", "endLine": 20, "endColumn": 19}, {"ruleId": "646", "severity": 1, "message": "671", "line": 47, "column": 12, "nodeType": "648", "messageId": "649", "endLine": 47, "endColumn": 30}, {"ruleId": "646", "severity": 1, "message": "672", "line": 5, "column": 23, "nodeType": "648", "messageId": "649", "endLine": 5, "endColumn": 38}, {"ruleId": "646", "severity": 1, "message": "673", "line": 10, "column": 7, "nodeType": "648", "messageId": "649", "endLine": 10, "endColumn": 19}, {"ruleId": "650", "severity": 1, "message": "674", "line": 33, "column": 8, "nodeType": "652", "endLine": 33, "endColumn": 10, "suggestions": "675"}, {"ruleId": "646", "severity": 1, "message": "676", "line": 20, "column": 10, "nodeType": "648", "messageId": "649", "endLine": 20, "endColumn": 20}, {"ruleId": "646", "severity": 1, "message": "677", "line": 20, "column": 22, "nodeType": "648", "messageId": "649", "endLine": 20, "endColumn": 35}, {"ruleId": "646", "severity": 1, "message": "678", "line": 3, "column": 20, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 28}, {"ruleId": "646", "severity": 1, "message": "679", "line": 3, "column": 37, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 43}, {"ruleId": "646", "severity": 1, "message": "680", "line": 3, "column": 45, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 52}, {"ruleId": "646", "severity": 1, "message": "681", "line": 13, "column": 21, "nodeType": "648", "messageId": "649", "endLine": 13, "endColumn": 31}, {"ruleId": "650", "severity": 1, "message": "682", "line": 40, "column": 8, "nodeType": "652", "endLine": 40, "endColumn": 41, "suggestions": "683"}, {"ruleId": "646", "severity": 1, "message": "684", "line": 17, "column": 12, "nodeType": "648", "messageId": "649", "endLine": 17, "endColumn": 21}, {"ruleId": "646", "severity": 1, "message": "678", "line": 3, "column": 20, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 28}, {"ruleId": "650", "severity": 1, "message": "685", "line": 39, "column": 8, "nodeType": "652", "endLine": 39, "endColumn": 42, "suggestions": "686"}, {"ruleId": "646", "severity": 1, "message": "687", "line": 3, "column": 34, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 41}, {"ruleId": "650", "severity": 1, "message": "688", "line": 23, "column": 8, "nodeType": "652", "endLine": 23, "endColumn": 19, "suggestions": "689"}, {"ruleId": "650", "severity": 1, "message": "690", "line": 32, "column": 8, "nodeType": "652", "endLine": 32, "endColumn": 30, "suggestions": "691"}, {"ruleId": "650", "severity": 1, "message": "692", "line": 43, "column": 8, "nodeType": "652", "endLine": 43, "endColumn": 10, "suggestions": "693"}, {"ruleId": "646", "severity": 1, "message": "694", "line": 11, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 11, "endColumn": 20}, {"ruleId": "646", "severity": 1, "message": "695", "line": 16, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 16, "endColumn": 12}, {"ruleId": "646", "severity": 1, "message": "696", "line": 17, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 17, "endColumn": 17}, {"ruleId": "646", "severity": 1, "message": "687", "line": 18, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 18, "endColumn": 12}, {"ruleId": "646", "severity": 1, "message": "697", "line": 1, "column": 60, "nodeType": "648", "messageId": "649", "endLine": 1, "endColumn": 66}, {"ruleId": "646", "severity": 1, "message": "698", "line": 28, "column": 12, "nodeType": "648", "messageId": "649", "endLine": 28, "endColumn": 26}, {"ruleId": "646", "severity": 1, "message": "699", "line": 28, "column": 28, "nodeType": "648", "messageId": "649", "endLine": 28, "endColumn": 45}, {"ruleId": "650", "severity": 1, "message": "700", "line": 83, "column": 8, "nodeType": "652", "endLine": 83, "endColumn": 10, "suggestions": "701"}, {"ruleId": "646", "severity": 1, "message": "702", "line": 4, "column": 10, "nodeType": "648", "messageId": "649", "endLine": 4, "endColumn": 22}, {"ruleId": "646", "severity": 1, "message": "660", "line": 3, "column": 19, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 30}, {"ruleId": "646", "severity": 1, "message": "703", "line": 14, "column": 12, "nodeType": "648", "messageId": "649", "endLine": 14, "endColumn": 24}, {"ruleId": "646", "severity": 1, "message": "704", "line": 93, "column": 11, "nodeType": "648", "messageId": "649", "endLine": 93, "endColumn": 23}, {"ruleId": "650", "severity": 1, "message": "705", "line": 84, "column": 8, "nodeType": "652", "endLine": 84, "endColumn": 18, "suggestions": "706"}, {"ruleId": "646", "severity": 1, "message": "707", "line": 113, "column": 9, "nodeType": "648", "messageId": "649", "endLine": 113, "endColumn": 25}, {"ruleId": "646", "severity": 1, "message": "708", "line": 147, "column": 9, "nodeType": "648", "messageId": "649", "endLine": 147, "endColumn": 22}, {"ruleId": "646", "severity": 1, "message": "709", "line": 4, "column": 10, "nodeType": "648", "messageId": "649", "endLine": 4, "endColumn": 17}, {"ruleId": "646", "severity": 1, "message": "710", "line": 13, "column": 10, "nodeType": "648", "messageId": "649", "endLine": 13, "endColumn": 17}, {"ruleId": "646", "severity": 1, "message": "711", "line": 14, "column": 10, "nodeType": "648", "messageId": "649", "endLine": 14, "endColumn": 15}, {"ruleId": "646", "severity": 1, "message": "708", "line": 115, "column": 9, "nodeType": "648", "messageId": "649", "endLine": 115, "endColumn": 22}, {"ruleId": "646", "severity": 1, "message": "712", "line": 132, "column": 9, "nodeType": "648", "messageId": "649", "endLine": 132, "endColumn": 19}, {"ruleId": "646", "severity": 1, "message": "713", "line": 145, "column": 9, "nodeType": "648", "messageId": "649", "endLine": 145, "endColumn": 22}, {"ruleId": "650", "severity": 1, "message": "685", "line": 44, "column": 8, "nodeType": "652", "endLine": 44, "endColumn": 21, "suggestions": "714"}, {"ruleId": "646", "severity": 1, "message": "715", "line": 10, "column": 10, "nodeType": "648", "messageId": "649", "endLine": 10, "endColumn": 23}, {"ruleId": "646", "severity": 1, "message": "716", "line": 10, "column": 25, "nodeType": "648", "messageId": "649", "endLine": 10, "endColumn": 41}, {"ruleId": "646", "severity": 1, "message": "717", "line": 18, "column": 25, "nodeType": "648", "messageId": "649", "endLine": 18, "endColumn": 46}, {"ruleId": "650", "severity": 1, "message": "718", "line": 19, "column": 8, "nodeType": "652", "endLine": 19, "endColumn": 10, "suggestions": "719"}, {"ruleId": "646", "severity": 1, "message": "702", "line": 4, "column": 10, "nodeType": "648", "messageId": "649", "endLine": 4, "endColumn": 22}, {"ruleId": "646", "severity": 1, "message": "720", "line": 7, "column": 14, "nodeType": "648", "messageId": "649", "endLine": 7, "endColumn": 20}, {"ruleId": "646", "severity": 1, "message": "696", "line": 7, "column": 41, "nodeType": "648", "messageId": "649", "endLine": 7, "endColumn": 53}, {"ruleId": "646", "severity": 1, "message": "721", "line": 8, "column": 46, "nodeType": "648", "messageId": "649", "endLine": 8, "endColumn": 52}, {"ruleId": "646", "severity": 1, "message": "722", "line": 306, "column": 11, "nodeType": "648", "messageId": "649", "endLine": 306, "endColumn": 27}, {"ruleId": "646", "severity": 1, "message": "723", "line": 4, "column": 45, "nodeType": "648", "messageId": "649", "endLine": 4, "endColumn": 64}, {"ruleId": "646", "severity": 1, "message": "724", "line": 4, "column": 66, "nodeType": "648", "messageId": "649", "endLine": 4, "endColumn": 79}, {"ruleId": "646", "severity": 1, "message": "725", "line": 4, "column": 111, "nodeType": "648", "messageId": "649", "endLine": 4, "endColumn": 123}, {"ruleId": "650", "severity": 1, "message": "726", "line": 29, "column": 8, "nodeType": "652", "endLine": 29, "endColumn": 10, "suggestions": "727"}, {"ruleId": "646", "severity": 1, "message": "728", "line": 256, "column": 11, "nodeType": "648", "messageId": "649", "endLine": 256, "endColumn": 26}, {"ruleId": "650", "severity": 1, "message": "729", "line": 24, "column": 8, "nodeType": "652", "endLine": 24, "endColumn": 33, "suggestions": "730"}, {"ruleId": "646", "severity": 1, "message": "720", "line": 5, "column": 57, "nodeType": "648", "messageId": "649", "endLine": 5, "endColumn": 63}, {"ruleId": "650", "severity": 1, "message": "731", "line": 24, "column": 8, "nodeType": "652", "endLine": 24, "endColumn": 33, "suggestions": "732"}, {"ruleId": "646", "severity": 1, "message": "733", "line": 121, "column": 19, "nodeType": "648", "messageId": "649", "endLine": 121, "endColumn": 28}, {"ruleId": "646", "severity": 1, "message": "734", "line": 136, "column": 19, "nodeType": "648", "messageId": "649", "endLine": 136, "endColumn": 22}, {"ruleId": "646", "severity": 1, "message": "735", "line": 4, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 4, "endColumn": 14}, {"ruleId": "646", "severity": 1, "message": "736", "line": 6, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 6, "endColumn": 10}, {"ruleId": "646", "severity": 1, "message": "664", "line": 7, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 7, "endColumn": 15}, {"ruleId": "646", "severity": 1, "message": "737", "line": 8, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 8, "endColumn": 16}, {"ruleId": "646", "severity": 1, "message": "738", "line": 57, "column": 9, "nodeType": "648", "messageId": "649", "endLine": 57, "endColumn": 26}, {"ruleId": "646", "severity": 1, "message": "663", "line": 2, "column": 10, "nodeType": "648", "messageId": "649", "endLine": 2, "endColumn": 24}, {"ruleId": "646", "severity": 1, "message": "739", "line": 2, "column": 26, "nodeType": "648", "messageId": "649", "endLine": 2, "endColumn": 34}, {"ruleId": "646", "severity": 1, "message": "740", "line": 3, "column": 35, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 41}, {"ruleId": "646", "severity": 1, "message": "741", "line": 5, "column": 25, "nodeType": "648", "messageId": "649", "endLine": 5, "endColumn": 39}, {"ruleId": "646", "severity": 1, "message": "742", "line": 5, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 5, "endColumn": 15}, {"ruleId": "646", "severity": 1, "message": "662", "line": 6, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 6, "endColumn": 18}, {"ruleId": "650", "severity": 1, "message": "743", "line": 29, "column": 8, "nodeType": "652", "endLine": 29, "endColumn": 31, "suggestions": "744"}, {"ruleId": "650", "severity": 1, "message": "685", "line": 31, "column": 8, "nodeType": "652", "endLine": 31, "endColumn": 17, "suggestions": "745"}, {"ruleId": "646", "severity": 1, "message": "746", "line": 3, "column": 33, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 38}, {"ruleId": "646", "severity": 1, "message": "747", "line": 14, "column": 12, "nodeType": "648", "messageId": "649", "endLine": 14, "endColumn": 21}, {"ruleId": "650", "severity": 1, "message": "748", "line": 29, "column": 8, "nodeType": "652", "endLine": 29, "endColumn": 10, "suggestions": "749"}, {"ruleId": "646", "severity": 1, "message": "750", "line": 3, "column": 41, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 62}, {"ruleId": "646", "severity": 1, "message": "750", "line": 3, "column": 40, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 61}, {"ruleId": "650", "severity": 1, "message": "751", "line": 28, "column": 8, "nodeType": "652", "endLine": 28, "endColumn": 24, "suggestions": "752"}, {"ruleId": "646", "severity": 1, "message": "687", "line": 3, "column": 51, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 58}, {"ruleId": "646", "severity": 1, "message": "736", "line": 3, "column": 102, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 107}, {"ruleId": "646", "severity": 1, "message": "753", "line": 18, "column": 12, "nodeType": "648", "messageId": "649", "endLine": 18, "endColumn": 21}, {"ruleId": "650", "severity": 1, "message": "754", "line": 23, "column": 8, "nodeType": "652", "endLine": 23, "endColumn": 17, "suggestions": "755"}, {"ruleId": "646", "severity": 1, "message": "756", "line": 3, "column": 40, "nodeType": "648", "messageId": "649", "endLine": 3, "endColumn": 46}, {"ruleId": "650", "severity": 1, "message": "757", "line": 30, "column": 8, "nodeType": "652", "endLine": 30, "endColumn": 10, "suggestions": "758"}, {"ruleId": "646", "severity": 1, "message": "759", "line": 6, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 6, "endColumn": 11}, {"ruleId": "650", "severity": 1, "message": "760", "line": 38, "column": 8, "nodeType": "652", "endLine": 38, "endColumn": 16, "suggestions": "761"}, {"ruleId": "650", "severity": 1, "message": "762", "line": 51, "column": 8, "nodeType": "652", "endLine": 51, "endColumn": 10, "suggestions": "763"}, {"ruleId": "764", "severity": 1, "message": "765", "line": 93, "column": 39, "nodeType": "766", "messageId": "767", "endLine": 93, "endColumn": 86}, {"ruleId": "646", "severity": 1, "message": "768", "line": 177, "column": 15, "nodeType": "648", "messageId": "649", "endLine": 177, "endColumn": 30}, {"ruleId": "646", "severity": 1, "message": "769", "line": 144, "column": 36, "nodeType": "648", "messageId": "649", "endLine": 144, "endColumn": 48}, {"ruleId": "646", "severity": 1, "message": "756", "line": 13, "column": 5, "nodeType": "648", "messageId": "649", "endLine": 13, "endColumn": 11}, {"ruleId": "646", "severity": 1, "message": "747", "line": 24, "column": 12, "nodeType": "648", "messageId": "649", "endLine": 24, "endColumn": 21}, {"ruleId": "650", "severity": 1, "message": "748", "line": 39, "column": 8, "nodeType": "652", "endLine": 39, "endColumn": 10, "suggestions": "770"}, "no-unused-vars", "'LeagueSelection' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'removeError'. Either include it or remove the dependency array.", "ArrayExpression", ["771"], "The 'publicRoutes' array makes the dependencies of useCallback Hook (at line 110) change on every render. To fix this, wrap the initialization of 'publicRoutes' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'setUserData'. Either include it or remove the dependency array.", ["772"], "React Hook useEffect has a missing dependency: 'publicRoutes'. Either include it or remove the dependency array.", ["773"], "'FaChartLine' is defined but never used.", "'navigate' is assigned a value but never used.", "'FaCalendarAlt' is defined but never used.", "'FaFootballBall' is defined but never used.", "'FaChartBar' is defined but never used.", "'FaExchangeAlt' is defined but never used.", "'FaEye' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'FaClock' is defined but never used.", "'FaBell' is defined but never used.", "'Bar' is defined but never used.", "'teamPopularityData' is assigned a value but never used.", "'currencyService' is defined but never used.", "'API_BASE_URL' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTeams'. Either include it or remove the dependency array.", ["774"], "'challenges' is assigned a value but never used.", "'setChallenges' is assigned a value but never used.", "'FaFilter' is defined but never used.", "'FaEdit' is defined but never used.", "'FaTrash' is defined but never used.", "'setSuccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllBets'. Either include it or remove the dependency array.", ["775"], "'otpExpiry' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLeaderboard'. Either include it or remove the dependency array.", ["776"], "'FaTimes' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchConversations'. Either include it or remove the dependency array.", ["777"], "React Hook useEffect has missing dependencies: 'fetchMessages' and 'markMessagesAsRead'. Either include them or remove the dependency array.", ["778"], "React Hook useEffect has a missing dependency: 'checkChallengeStatus'. Either include it or remove the dependency array.", ["779"], "'FaMoneyBillWave' is defined but never used.", "'FaUsers' is defined but never used.", "'FaInfoCircle' is defined but never used.", "'useRef' is defined but never used.", "'payoutPreviews' is assigned a value but never used.", "'setPayoutPreviews' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'fetchLeagueDetails'. Either include it or remove the dependency array.", ["780"], "'API_BASE_URL' is defined but never used.", "'loadingUsers' is assigned a value but never used.", "'currentUsers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserData'. Either include it or remove the dependency array.", ["781"], "'renderPagination' is assigned a value but never used.", "'calculateOdds' is assigned a value but never used.", "'FaCoins' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'formatDate' is assigned a value but never used.", "'getUserStatus' is assigned a value but never used.", ["782"], "'paymentMethod' is assigned a value but never used.", "'setPaymentMethod' is assigned a value but never used.", "'convertToUserCurrency' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPendingRequests'. Either include it or remove the dependency array.", ["783"], "'FaStar' is defined but never used.", "'FaFire' is defined but never used.", "'renderNavigation' is assigned a value but never used.", "'FaExclamationCircle' is defined but never used.", "'FaUserFriends' is defined but never used.", "'FaUserCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFriends'. Either include it or remove the dependency array.", ["784"], "'handleChallenge' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["785"], "React Hook useEffect has a missing dependency: 'fetchCreditRequests'. Either include it or remove the dependency array.", ["786"], "'matchDate' is assigned a value but never used.", "'now' is assigned a value but never used.", "'FaGamepad' is defined but never used.", "'FaCog' is defined but never used.", "'FaMoneyBill' is defined but never used.", "'handleLogoutClick' is assigned a value but never used.", "'FaTrophy' is defined but never used.", "'FaSave' is defined but never used.", "'refreshFavicon' is defined but never used.", "'FaDownload' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReport'. Either include it or remove the dependency array.", ["787"], ["788"], "'FaKey' is defined but never used.", "'secretKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initiate2FASetup'. Either include it or remove the dependency array.", ["789"], "'FaExclamationTriangle' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendInitialOTP'. Either include it or remove the dependency array.", ["790"], "'adminInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPreferences'. Either include it or remove the dependency array.", ["791"], "'FaCopy' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["792"], "'FaUser' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchUserBets', 'fetchUserDetails', and 'fetchUserTransactions'. Either include them or remove the dependency array.", ["793"], "React Hook useCallback has a missing dependency: 'CACHE_DURATION'. Either include it or remove the dependency array.", ["794"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'attempt'.", "ArrowFunctionExpression", "unsafeRefs", "'originalExecute' is assigned a value but never used.", "'userCurrency' is assigned a value but never used.", ["795"], {"desc": "796", "fix": "797"}, {"desc": "798", "fix": "799"}, {"desc": "800", "fix": "801"}, {"desc": "802", "fix": "803"}, {"desc": "804", "fix": "805"}, {"desc": "806", "fix": "807"}, {"desc": "808", "fix": "809"}, {"desc": "810", "fix": "811"}, {"desc": "812", "fix": "813"}, {"desc": "814", "fix": "815"}, {"desc": "816", "fix": "817"}, {"desc": "818", "fix": "819"}, {"desc": "820", "fix": "821"}, {"desc": "822", "fix": "823"}, {"desc": "824", "fix": "825"}, {"desc": "826", "fix": "827"}, {"desc": "828", "fix": "829"}, {"desc": "830", "fix": "831"}, {"desc": "832", "fix": "833"}, {"desc": "834", "fix": "835"}, {"desc": "836", "fix": "837"}, {"desc": "838", "fix": "839"}, {"desc": "840", "fix": "841"}, {"desc": "842", "fix": "843"}, {"desc": "832", "fix": "844"}, "Update the dependencies array to be: [removeError]", {"range": "845", "text": "846"}, "Update the dependencies array to be: [token, userId, setUserData, navigate, location.pathname]", {"range": "847", "text": "848"}, "Update the dependencies array to be: [token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, fetchSidebarLogo, publicRoutes]", {"range": "849", "text": "850"}, "Update the dependencies array to be: [fetchTeams]", {"range": "851", "text": "852"}, "Update the dependencies array to be: [pagination.currentPage, filters, fetchAllBets]", {"range": "853", "text": "854"}, "Update the dependencies array to be: [pagination.current_page, filters, fetchLeaderboard]", {"range": "855", "text": "856"}, "Update the dependencies array to be: [activeTab, fetchConversations]", {"range": "857", "text": "858"}, "Update the dependencies array to be: [fetchMessages, markMessagesAsRead, selectedConversation]", {"range": "859", "text": "860"}, "Update the dependencies array to be: [checkChallengeStatus]", {"range": "861", "text": "862"}, "Update the dependencies array to be: [fetchLeagueDetails]", {"range": "863", "text": "864"}, "Update the dependencies array to be: [fetchUserData, navigate]", {"range": "865", "text": "866"}, "Update the dependencies array to be: [currentPage, fetchLeaderboard]", {"range": "867", "text": "868"}, "Update the dependencies array to be: [fetchPendingRequests]", {"range": "869", "text": "870"}, "Update the dependencies array to be: [fetchFriends]", {"range": "871", "text": "872"}, "Update the dependencies array to be: [navigate, currentUserId, fetchInitialData]", {"range": "873", "text": "874"}, "Update the dependencies array to be: [navigate, currentUserId, fetchCreditRequests]", {"range": "875", "text": "876"}, "Update the dependencies array to be: [reportType, dateRange, fetchReport]", {"range": "877", "text": "878"}, "Update the dependencies array to be: [fetchLeaderboard, filters]", {"range": "879", "text": "880"}, "Update the dependencies array to be: [initiate2FASetup]", {"range": "881", "text": "882"}, "Update the dependencies array to be: [initialOtpSent, sendInitialOTP]", {"range": "883", "text": "884"}, "Update the dependencies array to be: [adminId, fetchPreferences]", {"range": "885", "text": "886"}, "Update the dependencies array to be: [fetchAdminData]", {"range": "887", "text": "888"}, "Update the dependencies array to be: [fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", {"range": "889", "text": "890"}, "Update the dependencies array to be: [CACHE_DURATION]", {"range": "891", "text": "892"}, {"range": "893", "text": "882"}, [985, 987], "[removeError]", [2754, 2798], "[token, userId, setUserData, navigate, location.pathname]", [4647, 4744], "[token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, fetchSidebarLogo, publicRoutes]", [1143, 1145], "[fetchTeams]", [1193, 1226], "[pagination.currentPage, filters, fetchAllBets]", [1249, 1283], "[pagination.current_page, filters, fetchLeaderboard]", [1071, 1082], "[activeTab, fetchConversations]", [1429, 1451], "[fetch<PERSON>essages, markMessagesAsRead, selectedConversation]", [1532, 1534], "[checkChallengeStatus]", [3005, 3007], "[fetchLeagueDetails]", [3404, 3414], "[fetch<PERSON><PERSON><PERSON><PERSON>, navigate]", [1160, 1173], "[currentPage, fetchLeaderboard]", [696, 698], "[fetchPendingRequests]", [1331, 1333], "[fetchFriends]", [856, 881], "[navigate, currentUserId, fetchInitialData]", [891, 916], "[navigate, currentUserId, fetchCreditRequests]", [864, 887], "[reportType, dateRange, fetchReport]", [887, 896], "[fetchLeaderboard, filters]", [1137, 1139], "[initiate2FASetup]", [1264, 1280], "[initialOtpSent, sendInitialOTP]", [856, 865], "[adminId, fetchPreferences]", [1269, 1271], "[fetchAdminData]", [1036, 1044], "[fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", [1873, 1875], "[CACHE_DURATION]", [1258, 1260]]