import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import UserLayout from '../components/UserLayout';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';

function UserSettingsSimple() {
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [userId, setUserId] = useState(null);
    const [userEmail, setUserEmail] = useState('');
    const [securitySettings, setSecuritySettings] = useState({
        otp_enabled: false,
        tfa_enabled: false,
        auth_method: 'password_only'
    });

    useEffect(() => {
        const storedUserId = localStorage.getItem('userId');
        if (!storedUserId) {
            navigate('/user/login');
            return;
        }
        
        setUserId(storedUserId);
        loadUserSettings();
    }, [navigate]);

    const loadUserSettings = async () => {
        try {
            setLoading(true);
            setError('');

            const userId = localStorage.getItem('userId');
            console.log('Loading settings for user:', userId);
            
            // Load user security settings
            const securityResponse = await axios.get(
                `${API_BASE_URL}/handlers/user_security_settings.php?userId=${userId}`
            );

            console.log('Security response:', securityResponse.data);

            if (securityResponse.data.success) {
                setSecuritySettings(securityResponse.data.settings);
                setUserEmail(securityResponse.data.user_email);
                setSuccess('Settings loaded successfully!');
            } else {
                setError('Failed to load security settings');
            }

        } catch (err) {
            console.error('Settings load error:', err);
            setError('Failed to load settings. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleOtpToggle = async () => {
        try {
            setError('');
            const newOtpState = !securitySettings.otp_enabled;
            
            const response = await axios.post(
                `${API_BASE_URL}/handlers/user_toggle_otp.php`,
                {
                    userId: userId,
                    enabled: newOtpState
                }
            );

            if (response.data.success) {
                setSecuritySettings(prev => ({
                    ...prev,
                    otp_enabled: newOtpState
                }));
                setSuccess(`OTP ${newOtpState ? 'enabled' : 'disabled'} successfully`);
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to update OTP setting');
            }
        } catch (err) {
            setError('Failed to update OTP setting. Please try again.');
        }
    };

    if (loading) {
        return (
            <UserLayout>
                <div style={{ padding: '20px', textAlign: 'center' }}>
                    <h2>Loading settings...</h2>
                </div>
            </UserLayout>
        );
    }

    return (
        <UserLayout>
            <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
                <h1>🔧 User Settings (Simple Test)</h1>
                <p>Manage your account security and preferences</p>

                {error && (
                    <div style={{ 
                        backgroundColor: '#fed7d7', 
                        color: '#c53030', 
                        padding: '12px', 
                        borderRadius: '8px', 
                        marginBottom: '20px' 
                    }}>
                        ❌ {error}
                    </div>
                )}

                {success && (
                    <div style={{ 
                        backgroundColor: '#c6f6d5', 
                        color: '#2f855a', 
                        padding: '12px', 
                        borderRadius: '8px', 
                        marginBottom: '20px' 
                    }}>
                        ✅ {success}
                    </div>
                )}

                <div style={{ 
                    backgroundColor: 'white', 
                    padding: '20px', 
                    borderRadius: '8px', 
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                    marginBottom: '20px'
                }}>
                    <h2>🔐 Security Settings</h2>
                    <p>User: {userEmail}</p>
                    <p>User ID: {userId}</p>
                    
                    <div style={{ marginBottom: '20px' }}>
                        <h3>📧 Email OTP (One-Time Password)</h3>
                        <p>Current status: {securitySettings.otp_enabled ? '✅ Enabled' : '❌ Disabled'}</p>
                        <button 
                            onClick={handleOtpToggle}
                            style={{
                                backgroundColor: securitySettings.otp_enabled ? '#e53e3e' : '#38a169',
                                color: 'white',
                                padding: '10px 20px',
                                border: 'none',
                                borderRadius: '6px',
                                cursor: 'pointer'
                            }}
                        >
                            {securitySettings.otp_enabled ? 'Disable OTP' : 'Enable OTP'}
                        </button>
                    </div>

                    <div style={{ marginBottom: '20px' }}>
                        <h3>🔐 Two-Factor Authentication (2FA)</h3>
                        <p>Current status: {securitySettings.tfa_enabled ? '✅ Enabled' : '❌ Disabled'}</p>
                        <p>Auth Method: {securitySettings.auth_method}</p>
                        <button 
                            disabled
                            style={{
                                backgroundColor: '#a0aec0',
                                color: 'white',
                                padding: '10px 20px',
                                border: 'none',
                                borderRadius: '6px',
                                cursor: 'not-allowed'
                            }}
                        >
                            Setup 2FA (Coming Soon)
                        </button>
                    </div>
                </div>

                <div style={{ 
                    backgroundColor: '#f7fafc', 
                    padding: '20px', 
                    borderRadius: '8px',
                    border: '1px solid #e2e8f0'
                }}>
                    <h3>ℹ️ Debug Information</h3>
                    <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                        {JSON.stringify({ userId, userEmail, securitySettings }, null, 2)}
                    </pre>
                </div>
            </div>
        </UserLayout>
    );
}

export default UserSettingsSimple;
