.user-2fa-setup {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.setup-header {
    text-align: center;
    margin-bottom: 32px;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: #52B788;
    font-size: 14px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    margin-bottom: 16px;
}

.back-btn:hover {
    background-color: #f0f9f4;
}

.setup-header h1 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 28px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 24px 0;
}

.setup-steps {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 16px;
}

.step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 16px;
    background-color: #e2e8f0;
    color: #718096;
    transition: all 0.3s ease;
}

.step.active {
    background-color: #52B788;
    color: white;
}

.setup-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #52B788;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.setup-alert {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    font-weight: 500;
}

.setup-alert-error {
    background-color: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.setup-alert-success {
    background-color: #c6f6d5;
    color: #2f855a;
    border: 1px solid #9ae6b4;
}

.setup-step {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.step-header {
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid #e2e8f0;
    text-align: center;
}

.step-header h2 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 22px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 8px 0;
}

.step-header p {
    color: #718096;
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
}

.setup-content {
    padding: 32px 24px;
}

.qr-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    align-items: start;
}

.qr-code-container {
    text-align: center;
}

.qr-code {
    max-width: 200px;
    width: 100%;
    height: auto;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    background: white;
}

.manual-entry {
    background: #f7fafc;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.manual-entry h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 8px 0;
}

.manual-entry p {
    color: #718096;
    font-size: 14px;
    margin: 0 0 16px 0;
    line-height: 1.5;
}

.secret-key-container {
    display: flex;
    align-items: center;
    gap: 8px;
    background: white;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.secret-key {
    flex: 1;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    color: #2d3748;
    word-break: break-all;
    background: none;
    border: none;
    padding: 0;
}

.copy-btn {
    background: none;
    border: none;
    color: #52B788;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    font-size: 14px;
}

.copy-btn:hover {
    background-color: #f0f9f4;
}

.setup-info {
    margin-top: 24px;
}

.info-card {
    display: flex;
    gap: 12px;
    background: #ebf8ff;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #3182ce;
}

.info-icon {
    color: #3182ce;
    font-size: 16px;
    margin-top: 2px;
    flex-shrink: 0;
}

.info-card h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 8px 0;
}

.info-card ol {
    margin: 0;
    padding-left: 16px;
    color: #4a5568;
    font-size: 14px;
    line-height: 1.5;
}

.verification-form {
    max-width: 300px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
}

.verification-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 18px;
    text-align: center;
    letter-spacing: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    transition: border-color 0.2s ease;
}

.verification-input:focus {
    outline: none;
    border-color: #52B788;
}

.backup-codes-section {
    max-width: 600px;
    margin: 0 auto;
}

.backup-codes-container {
    background: #f7fafc;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    margin-bottom: 20px;
}

.backup-codes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.backup-codes-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.backup-codes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

.backup-code {
    background: white;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    text-align: center;
    color: #2d3748;
}

.backup-warning {
    display: flex;
    gap: 12px;
    background: #fef5e7;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #f6ad55;
}

.warning-icon {
    color: #f6ad55;
    font-size: 16px;
    margin-top: 2px;
    flex-shrink: 0;
}

.backup-warning div {
    font-size: 14px;
    line-height: 1.5;
    color: #744210;
}

.backup-warning strong {
    color: #744210;
}

.step-actions {
    padding: 24px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    gap: 12px;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background-color: #52B788;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #2C5F2D;
}

.btn-primary:disabled {
    background-color: #a0aec0;
    cursor: not-allowed;
}

.btn-secondary {
    background-color: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background-color: #cbd5e0;
}

.btn-success {
    background-color: #38a169;
    color: white;
}

.btn-success:hover {
    background-color: #2f855a;
}

/* Responsive Design */
@media (max-width: 768px) {
    .user-2fa-setup {
        padding: 16px;
    }
    
    .qr-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .setup-content {
        padding: 20px 16px;
    }
    
    .step-actions {
        flex-direction: column;
    }
    
    .backup-codes-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .setup-header h1 {
        font-size: 24px;
    }
    
    .step-header h2 {
        font-size: 20px;
    }
}
