import React, { useState, useEffect } from 'react';
import axios from '../utils/axiosConfig';
import {
  FaSave,
  FaTrash,
  FaExchangeAlt,
  FaClock,
  FaCalendarAlt,
  FaFutbol,
  FaInfoCircle,
  FaCheckCircle,
  FaExclamationTriangle,
  FaHome,
  FaPlane
} from 'react-icons/fa';
import { API_BASE_URL } from '../config';
import './ChallengeSystem.css';

function ChallengeSystem() {
  const [challenges, setChallenges] = useState([]);
  const [teams, setTeams] = useState([]);
  const [newChallenge, setNewChallenge] = useState({
    team1: '',
    team2: '',
    odds1: 1.8,
    odds2: 1.8,
    goalAdvantage1: 0,
    goalAdvantage2: 0,
    startTime: '',
    endTime: '',
    matchTime: '',
    matchType: 'full_time',
    oddsDraw: 0.8,
    oddsLost: 0.2,
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    fetchTeams();
  }, []);

  const fetchTeams = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);
      setTeams(response.data.data);
    } catch (err) {
      console.error("Error fetching teams:", err);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewChallenge(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!newChallenge.team1 || !newChallenge.team2 || !newChallenge.odds1 || !newChallenge.odds2) {
      setError('Team names and odds are required.');
      return;
    }

    try {
      const formData = new FormData();
      const team1Data = teams.find(t => t.name === newChallenge.team1);
      const team2Data = teams.find(t => t.name === newChallenge.team2);

      formData.append('team1', newChallenge.team1);
      formData.append('team2', newChallenge.team2);
      formData.append('odds1', newChallenge.odds1);
      formData.append('odds2', newChallenge.odds2);
      formData.append('goalAdvantage1', newChallenge.goalAdvantage1);
      formData.append('goalAdvantage2', newChallenge.goalAdvantage2);
      formData.append('startTime', newChallenge.startTime);
      formData.append('endTime', newChallenge.endTime);
      formData.append('matchTime', newChallenge.matchTime);
      formData.append('matchType', newChallenge.matchType);
      formData.append('oddsDraw', newChallenge.oddsDraw);
      formData.append('oddsLost', newChallenge.oddsLost);
      formData.append('logo1', team1Data ? `${API_BASE_URL}/${team1Data.logo}` : '');
      formData.append('logo2', team2Data ? `${API_BASE_URL}/${team2Data.logo}` : '');

      const response = await axios.post('create_challenge.php', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      if (response.data.success) {
        setSuccess('Challenge created successfully!');
        setNewChallenge({
          team1: '',
          team2: '',
          odds1: 1.8,
          odds2: 1.8,
          goalAdvantage1: 0,
          goalAdvantage2: 0,
          startTime: '',
          endTime: '',
          matchTime: '',
          matchType: 'full_time',
          oddsDraw: 0.8,
          oddsLost: 0.2,
        });
        setTimeout(() => {
          setSuccess('');
        }, 3000);
      } else {
        setError(response.data.message || 'Failed to create challenge');
        setTimeout(() => {
          setError('');
        }, 3000);
      }
    } catch (err) {
      setError('Failed to create challenge');
    }
  };

  const handleDiscard = () => {
    setNewChallenge({
      team1: '', team2: '', odds1: 1.8, odds2: 1.8,
      goalAdvantage1: 0, goalAdvantage2: 0,
      startTime: '', endTime: '', matchTime: '',
      matchType: 'full_time',
      oddsDraw: 0.8,
      oddsLost: 0.2,
    });
    setError('');
    setSuccess('');
  };

  return (
    <div className="challenge-system">
      <h1>Create a New Challenge</h1>
      <div className="header-actions">
        <button onClick={handleSubmit} style={{ backgroundColor: '#166534', color: 'white' }}>
          <FaSave />
          <span>Save Challenge</span>
        </button>
        <button onClick={handleDiscard} style={{ backgroundColor: '#dc2626', color: 'white', border: 'none' }}>
          <FaTrash />
          <span>Discard Changes</span>
        </button>
      </div>
      {error && (
        <div className="error-message">
          <FaExclamationTriangle />
          {error}
        </div>
      )}
      {success && (
        <div className="success-message">
          <FaCheckCircle />
          {success}
        </div>
      )}
      <form onSubmit={handleSubmit} className="challenge-form">
        <div className="match-settings">
          <div className="match-type-section">
            <h3><FaFutbol /> Match Settings</h3>
            <div className="match-settings-grid">
              <div className="form-group">
                <label htmlFor="matchType" className="required-field">Match Type:</label>
                <select
                  id="matchType"
                  name="matchType"
                  value={newChallenge.matchType}
                  onChange={handleInputChange}
                  required
                >
                  <option value="full_time">Full Time</option>
                  <option value="half_time">Half Time</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="oddsDraw">
                  Draw Odds:
                  <span className="info-tooltip" title="Multiplier for draw results">
                    <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />
                  </span>
                </label>
                <input
                  type="number"
                  id="oddsDraw"
                  name="oddsDraw"
                  value={newChallenge.oddsDraw}
                  onChange={handleInputChange}
                  step="0.1"
                  min="0"
                />
                <p className="odds-explanation">Default: 0.8</p>
              </div>
              <div className="form-group">
                <label htmlFor="oddsLost">
                  Lost Odds:
                  <span className="info-tooltip" title="Multiplier for lost results">
                    <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />
                  </span>
                </label>
                <input
                  type="number"
                  id="oddsLost"
                  name="oddsLost"
                  value={newChallenge.oddsLost}
                  onChange={handleInputChange}
                  step="0.1"
                  min="0"
                />
                <p className="odds-explanation">Default: 0.2</p>
              </div>
            </div>
          </div>
        </div>

        <div style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: '20px',
            width: '100%',
            position: 'relative',
            alignItems: 'flex-start'
          }}>
          <div style={{
            flex: '1',
            backgroundColor: 'white',
            borderRadius: '6px',
            padding: '1.25rem',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
            border: '1px solid #e5e7eb',
            display: 'flex',
            flexDirection: 'column',
            gap: '0.75rem',
            width: '48%',
            maxWidth: '48%'
          }} className="team-section team1">
            <h3><FaHome /> HOME TEAM</h3>
            <label htmlFor="team1" className="required-field">Select Team 1:</label>
            <select id="team1" name="team1" value={newChallenge.team1} onChange={handleInputChange} required>
              <option value="">Select a team</option>
              {teams.map((team) => (
                <option key={team.id} value={team.name}>
                  {team.name}
                </option>
              ))}
            </select>

            {newChallenge.team1 ? (
              <div className="logo-container">
                <img
                  src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team1)?.logo}`}
                  alt="Team 1 Logo"
                  className="logo-preview"
                />
              </div>
            ) : (
              <div className="logo-container empty-logo">
                <FaHome size={40} style={{ color: '#d1d5db' }} />
              </div>
            )}

            <label htmlFor="odds1" className="required-field">
              Odds for Team 1:
              <span className="info-tooltip" title="Multiplier for team 1 win">
                <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />
              </span>
            </label>
            <input
              type="number"
              id="odds1"
              name="odds1"
              value={newChallenge.odds1}
              onChange={handleInputChange}
              required
              step="0.01"
              min="1"
            />
            <p className="odds-explanation">
              User's bet x {newChallenge.odds1} = Potential winnings
            </p>

            <label htmlFor="goalAdvantage1">Goal Advantage:</label>
            <input
              type="number"
              id="goalAdvantage1"
              name="goalAdvantage1"
              value={newChallenge.goalAdvantage1}
              onChange={handleInputChange}
            />
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '40px',
            height: '40px',
            backgroundColor: '#166534',
            borderRadius: '50%',
            color: 'white',
            fontSize: '1rem',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            zIndex: '10',
            alignSelf: 'center',
            margin: '0 10px'
          }} className="vs-divider">
            <FaExchangeAlt />
          </div>

          <div style={{
            flex: '1',
            backgroundColor: 'white',
            borderRadius: '6px',
            padding: '1.25rem',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
            border: '1px solid #e5e7eb',
            display: 'flex',
            flexDirection: 'column',
            gap: '0.75rem',
            width: '48%',
            maxWidth: '48%'
          }} className="team-section team2">
            <h3><FaPlane /> AWAY TEAM</h3>
            <label htmlFor="team2" className="required-field">Select Team 2:</label>
            <select id="team2" name="team2" value={newChallenge.team2} onChange={handleInputChange} required>
              <option value="">Select a team</option>
              {teams.map((team) => (
                <option key={team.id} value={team.name}>
                  {team.name}
                </option>
              ))}
            </select>

            {newChallenge.team2 ? (
              <div className="logo-container">
                <img
                  src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team2)?.logo}`}
                  alt="Team 2 Logo"
                  className="logo-preview"
                />
              </div>
            ) : (
              <div className="logo-container empty-logo">
                <FaPlane size={40} style={{ color: '#d1d5db' }} />
              </div>
            )}

            <label htmlFor="odds2" className="required-field">
              Odds for Team 2:
              <span className="info-tooltip" title="Multiplier for team 2 win">
                <FaInfoCircle size={12} style={{ marginLeft: '5px', color: '#6b7280' }} />
              </span>
            </label>
            <input
              type="number"
              id="odds2"
              name="odds2"
              value={newChallenge.odds2}
              onChange={handleInputChange}
              required
              step="0.01"
              min="1"
            />
            <p className="odds-explanation">
              User's bet x {newChallenge.odds2} = Potential winnings
            </p>

            <label htmlFor="goalAdvantage2">Goal Advantage:</label>
            <input
              type="number"
              id="goalAdvantage2"
              name="goalAdvantage2"
              value={newChallenge.goalAdvantage2}
              onChange={handleInputChange}
            />
          </div>
        </div>

        <div className="time-section">
          <h3><FaClock /> Time Settings</h3>

          <div className="time-groups-container">
            <div className="time-group">
              <label htmlFor="startTime" className="required-field">
                <FaCalendarAlt className="time-icon" /> Challenge Start Time:
              </label>
              <input
                type="datetime-local"
                id="startTime"
                name="startTime"
                value={newChallenge.startTime}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="time-group">
              <label htmlFor="endTime" className="required-field">
                <FaCalendarAlt className="time-icon" /> Challenge End Time:
              </label>
              <input
                type="datetime-local"
                id="endTime"
                name="endTime"
                value={newChallenge.endTime}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="time-group">
              <label htmlFor="matchTime" className="required-field">
                <FaCalendarAlt className="time-icon" /> Actual Match Time:
              </label>
              <input
                type="datetime-local"
                id="matchTime"
                name="matchTime"
                value={newChallenge.matchTime}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>
        </div>
      </form>

      <div className="challenge-preview">
        <h3><FaFutbol /> Challenge Preview</h3>

        {(!newChallenge.team1 || !newChallenge.team2) ? (
          <div className="empty-preview">
            <FaInfoCircle />
            <p>Select teams to see the challenge preview</p>
          </div>
        ) : (
          <>
            <div style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              gap: '20px',
              width: '100%',
              position: 'relative',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <div style={{
                flex: '1',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textAlign: 'center',
                width: '48%',
                maxWidth: '48%'
              }} className="preview-team">
                <div className="logo-container">
                  <img
                    src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team1)?.logo}`}
                    alt="Team 1 Logo"
                    className="logo-preview"
                  />
                </div>
                <div className="preview-team-name">{newChallenge.team1}</div>
                <div className="preview-odds">Odds: {newChallenge.odds1}</div>
                {newChallenge.goalAdvantage1 > 0 && (
                  <div className="preview-advantage">+{newChallenge.goalAdvantage1} Goal Advantage</div>
                )}
              </div>

              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px',
                backgroundColor: '#166534',
                borderRadius: '50%',
                color: 'white',
                fontSize: '1.25rem',
                fontWeight: '700',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                margin: '0 10px'
              }} className="preview-vs">VS</div>

              <div style={{
                flex: '1',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textAlign: 'center',
                width: '48%',
                maxWidth: '48%'
              }} className="preview-team">
                <div className="logo-container">
                  <img
                    src={`${API_BASE_URL}/${teams.find(t => t.name === newChallenge.team2)?.logo}`}
                    alt="Team 2 Logo"
                    className="logo-preview"
                  />
                </div>
                <div className="preview-team-name">{newChallenge.team2}</div>
                <div className="preview-odds">Odds: {newChallenge.odds2}</div>
                {newChallenge.goalAdvantage2 > 0 && (
                  <div className="preview-advantage">+{newChallenge.goalAdvantage2} Goal Advantage</div>
                )}
              </div>
            </div>

            <div className="preview-time-details">
              <p>
                <span>Start Time</span>
                <FaCalendarAlt className="time-detail-icon" />
                {newChallenge.startTime ? new Date(newChallenge.startTime).toLocaleString() : 'Not set'}
              </p>
              <p>
                <span>End Time</span>
                <FaCalendarAlt className="time-detail-icon" />
                {newChallenge.endTime ? new Date(newChallenge.endTime).toLocaleString() : 'Not set'}
              </p>
              <p>
                <span>Match Time</span>
                <FaCalendarAlt className="time-detail-icon" />
                {newChallenge.matchTime ? new Date(newChallenge.matchTime).toLocaleString() : 'Not set'}
              </p>
              <p>
                <span>Match Type</span>
                <FaFutbol className="time-detail-icon" />
                {newChallenge.matchType.replace('_', ' ').toUpperCase()}
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default ChallengeSystem;
