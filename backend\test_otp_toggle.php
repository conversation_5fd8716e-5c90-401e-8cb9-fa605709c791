<?php
header('Content-Type: text/plain');
require_once 'includes/db_connect.php';

try {
    echo "🧪 Testing OTP Toggle Functionality...\n\n";
    
    $conn = getDBConnection();
    $userId = 11; // testuser
    
    echo "📋 Current user status:\n";
    $stmt = $conn->prepare("SELECT user_id, username, email, otp_enabled, tfa_enabled, auth_method FROM users WHERE user_id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "✅ User: {$user['username']} ({$user['email']})\n";
        echo "   OTP Enabled: " . ($user['otp_enabled'] ? 'Yes' : 'No') . "\n";
        echo "   2FA Enabled: " . ($user['tfa_enabled'] ? 'Yes' : 'No') . "\n";
        echo "   Auth Method: " . ($user['auth_method'] ?? 'password_only') . "\n\n";
    } else {
        echo "❌ User not found\n";
        exit(1);
    }
    
    // Test enabling OTP
    echo "🔄 Testing OTP Enable...\n";
    $conn->beginTransaction();
    
    $stmt = $conn->prepare("UPDATE users SET otp_enabled = 1 WHERE user_id = ?");
    $stmt->execute([$userId]);
    
    $newAuthMethod = $user['tfa_enabled'] ? 'password_otp_2fa' : 'password_otp';
    $stmt = $conn->prepare("UPDATE users SET auth_method = ? WHERE user_id = ?");
    $stmt->execute([$newAuthMethod, $userId]);
    
    // Log the change
    $stmt = $conn->prepare("
        INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, 'otp', 'otp_enabled', ?, ?, ?)
    ");
    $stmt->execute([
        $userId,
        json_encode(['otp_enabled' => true, 'test' => true]),
        '127.0.0.1',
        'Test Script'
    ]);
    
    $conn->commit();
    echo "✅ OTP enabled successfully\n";
    
    // Verify the change
    $stmt = $conn->prepare("SELECT otp_enabled, auth_method FROM users WHERE user_id = ?");
    $stmt->execute([$userId]);
    $updated = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   New OTP Status: " . ($updated['otp_enabled'] ? 'Enabled' : 'Disabled') . "\n";
    echo "   New Auth Method: " . $updated['auth_method'] . "\n\n";
    
    // Test disabling OTP
    echo "🔄 Testing OTP Disable...\n";
    $conn->beginTransaction();
    
    $stmt = $conn->prepare("UPDATE users SET otp_enabled = 0 WHERE user_id = ?");
    $stmt->execute([$userId]);
    
    $newAuthMethod = $user['tfa_enabled'] ? 'password_2fa' : 'password_only';
    $stmt = $conn->prepare("UPDATE users SET auth_method = ? WHERE user_id = ?");
    $stmt->execute([$newAuthMethod, $userId]);
    
    // Log the change
    $stmt = $conn->prepare("
        INSERT INTO user_auth_logs (user_id, auth_type, action, details, ip_address, user_agent) 
        VALUES (?, 'otp', 'otp_disabled', ?, ?, ?)
    ");
    $stmt->execute([
        $userId,
        json_encode(['otp_enabled' => false, 'test' => true]),
        '127.0.0.1',
        'Test Script'
    ]);
    
    $conn->commit();
    echo "✅ OTP disabled successfully\n";
    
    // Verify the change
    $stmt = $conn->prepare("SELECT otp_enabled, auth_method FROM users WHERE user_id = ?");
    $stmt->execute([$userId]);
    $final = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   Final OTP Status: " . ($final['otp_enabled'] ? 'Enabled' : 'Disabled') . "\n";
    echo "   Final Auth Method: " . $final['auth_method'] . "\n\n";
    
    // Check auth logs
    echo "📋 Recent auth logs:\n";
    $stmt = $conn->prepare("
        SELECT auth_type, action, details, created_at 
        FROM user_auth_logs 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$userId]);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($logs as $log) {
        echo "   {$log['created_at']}: {$log['auth_type']} - {$log['action']}\n";
        $details = json_decode($log['details'], true);
        if ($details) {
            echo "     Details: " . json_encode($details) . "\n";
        }
    }
    
    echo "\n🎉 OTP Toggle Test Completed Successfully!\n";
    echo "✅ Database operations working correctly\n";
    echo "✅ Auth method updates working correctly\n";
    echo "✅ Logging system working correctly\n";
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
