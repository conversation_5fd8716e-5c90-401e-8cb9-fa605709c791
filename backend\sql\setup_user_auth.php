<?php
// Setup script for user authentication tables
require_once '../includes/db_connect.php';

try {
    $conn = initializeDatabase();
    
    echo "🚀 Setting up User Authentication System...\n\n";
    
    // Read and execute the user authentication schema
    $schemaFile = __DIR__ . '/user_authentication_schema.sql';
    
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: $schemaFile");
    }
    
    $sql = file_get_contents($schemaFile);
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    $conn->beginTransaction();
    
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $conn->exec($statement);
                
                // Extract table name for logging
                if (preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches)) {
                    echo "✅ Created table: {$matches[1]}\n";
                } elseif (preg_match('/ALTER TABLE.*?`?(\w+)`?/i', $statement, $matches)) {
                    echo "✅ Modified table: {$matches[1]}\n";
                } elseif (preg_match('/INSERT INTO.*?`?(\w+)`?/i', $statement, $matches)) {
                    echo "✅ Inserted data into: {$matches[1]}\n";
                }
            } catch (PDOException $e) {
                // Skip if table already exists or column already exists
                if (strpos($e->getMessage(), 'already exists') !== false || 
                    strpos($e->getMessage(), 'Duplicate column') !== false) {
                    continue;
                }
                throw $e;
            }
        }
    }
    
    $conn->commit();
    
    echo "\n🎉 User Authentication System setup completed successfully!\n\n";
    
    // Verify tables were created
    echo "📋 Verifying table creation:\n";
    
    $tables = ['user_2fa', 'user_otp', 'user_login_attempts', 'user_auth_logs', 'user_auth_settings'];
    
    foreach ($tables as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' exists\n";
            
            // Show table structure
            $stmt = $conn->prepare("DESCRIBE $table");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "   Columns: " . implode(', ', $columns) . "\n";
        } else {
            echo "❌ Table '$table' not found\n";
        }
    }
    
    // Check if users table has new columns
    echo "\n📋 Checking users table modifications:\n";
    $stmt = $conn->prepare("DESCRIBE users");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $newColumns = ['otp_enabled', 'tfa_enabled', 'auth_method'];
    foreach ($newColumns as $column) {
        $found = false;
        foreach ($columns as $col) {
            if ($col['Field'] === $column) {
                echo "✅ Column '$column' exists in users table\n";
                $found = true;
                break;
            }
        }
        if (!$found) {
            echo "❌ Column '$column' not found in users table\n";
        }
    }
    
    echo "\n🔧 Setup Summary:\n";
    echo "- User 2FA table created for Google Authenticator support\n";
    echo "- User OTP table created for email verification codes\n";
    echo "- User login attempts table created for rate limiting\n";
    echo "- User auth logs table created for security auditing\n";
    echo "- User auth settings table created for user preferences\n";
    echo "- Users table enhanced with authentication columns\n";
    
    echo "\n✨ Your FanBet247 user authentication system is ready!\n";
    echo "Users can now:\n";
    echo "- Enable/disable OTP via email\n";
    echo "- Set up Google Authenticator 2FA\n";
    echo "- Manage their security preferences\n";
    echo "- Benefit from enhanced login security\n\n";
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    
    echo "❌ Error setting up user authentication system:\n";
    echo $e->getMessage() . "\n";
    echo "\nPlease check your database connection and try again.\n";
    exit(1);
}
?>
