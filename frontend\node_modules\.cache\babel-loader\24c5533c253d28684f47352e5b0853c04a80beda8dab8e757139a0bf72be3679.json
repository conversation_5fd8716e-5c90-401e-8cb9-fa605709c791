{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\User\\\\User2FASetup.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaQrcode, FaKey, FaCopy, FaCheck, FaArrowLeft, FaShieldAlt, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';\nimport './User2FASetup.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\nconst User2FASetup = ({\n  userId,\n  userEmail,\n  onSuccess,\n  onBack\n}) => {\n  _s();\n  const [step, setStep] = useState(1); // 1: Setup, 2: Verify, 3: Backup Codes\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Setup data\n  const [secretKey, setSecretKey] = useState('');\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\n  const [backupCodes, setBackupCodes] = useState([]);\n  const [manualEntryKey, setManualEntryKey] = useState('');\n\n  // Verification\n  const [verificationCode, setVerificationCode] = useState('');\n  const [verifying, setVerifying] = useState(false);\n\n  // UI state\n  const [copied, setCopied] = useState(false);\n  const [backupCodesCopied, setBackupCodesCopied] = useState(false);\n  useEffect(() => {\n    initiate2FASetup();\n  }, []);\n  const initiate2FASetup = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await axios.get(`${API_BASE_URL}/handlers/user_setup_2fa.php?userId=${userId}`);\n      if (response.data.success) {\n        setSecretKey(response.data.secret_key);\n        setQrCodeUrl(response.data.qr_code_url);\n        setBackupCodes(response.data.backup_codes);\n        setManualEntryKey(response.data.manual_entry_key);\n        setSuccess('2FA setup initiated successfully');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to initiate 2FA setup');\n      }\n    } catch (err) {\n      setError('Failed to initiate 2FA setup. Please try again.');\n      console.error('2FA setup error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const verify2FASetup = async () => {\n    if (!verificationCode || verificationCode.length !== 6) {\n      setError('Please enter a valid 6-digit code from your authenticator app');\n      return;\n    }\n    try {\n      setVerifying(true);\n      setError('');\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_setup_2fa.php?userId=${userId}`, {\n        verification_code: verificationCode\n      });\n      if (response.data.success) {\n        setSuccess('2FA setup completed successfully!');\n        setStep(3); // Show backup codes\n      } else {\n        setError(response.data.message || 'Invalid verification code');\n      }\n    } catch (err) {\n      setError('Failed to verify 2FA setup. Please try again.');\n      console.error('2FA verification error:', err);\n    } finally {\n      setVerifying(false);\n    }\n  };\n  const copyToClipboard = async (text, type = 'secret') => {\n    try {\n      await navigator.clipboard.writeText(text);\n      if (type === 'secret') {\n        setCopied(true);\n        setTimeout(() => setCopied(false), 2000);\n      } else if (type === 'backup') {\n        setBackupCodesCopied(true);\n        setTimeout(() => setBackupCodesCopied(false), 2000);\n      }\n    } catch (err) {\n      console.error('Failed to copy to clipboard:', err);\n    }\n  };\n  const handleComplete = () => {\n    onSuccess();\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"user-2fa-setup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setup-loading\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Setting up 2FA...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-2fa-setup\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"setup-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onBack,\n        className: \"back-btn\",\n        children: [/*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 21\n        }, this), \" Back to Settings\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 21\n        }, this), \" Two-Factor Authentication Setup\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setup-steps\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `step ${step >= 1 ? 'active' : ''}`,\n          children: \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `step ${step >= 2 ? 'active' : ''}`,\n          children: \"2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `step ${step >= 3 ? 'active' : ''}`,\n          children: \"3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"setup-alert setup-alert-error\",\n      children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"setup-alert setup-alert-success\",\n      children: [/*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 17\n    }, this), step === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"setup-step\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"step-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(FaQrcode, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 29\n          }, this), \" Step 1: Scan QR Code\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Use Google Authenticator or any compatible 2FA app to scan this QR code\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setup-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qr-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qr-code-container\",\n            children: qrCodeUrl && /*#__PURE__*/_jsxDEV(\"img\", {\n              src: qrCodeUrl,\n              alt: \"2FA QR Code\",\n              className: \"qr-code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"manual-entry\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: [/*#__PURE__*/_jsxDEV(FaKey, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 37\n              }, this), \" Manual Entry\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"If you can't scan the QR code, enter this key manually:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"secret-key-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"code\", {\n                className: \"secret-key\",\n                children: manualEntryKey\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => copyToClipboard(manualEntryKey, 'secret'),\n                className: \"copy-btn\",\n                title: \"Copy to clipboard\",\n                children: copied ? /*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 51\n                }, this) : /*#__PURE__*/_jsxDEV(FaCopy, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 65\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"setup-info\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"info-card\",\n            children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n              className: \"info-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: \"Setup Instructions:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Download Google Authenticator or similar app\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Scan the QR code or enter the key manually\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Your app will generate 6-digit codes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Enter a code in the next step to verify\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"step-actions\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setStep(2),\n          className: \"btn btn-primary\",\n          disabled: !qrCodeUrl,\n          children: \"Next: Verify Setup\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 17\n    }, this), step === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"setup-step\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"step-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 29\n          }, this), \" Step 2: Verify Setup\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Enter the 6-digit code from your authenticator app to verify the setup\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setup-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"verification-form\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"verificationCode\",\n              children: \"Verification Code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"verificationCode\",\n              value: verificationCode,\n              onChange: e => setVerificationCode(e.target.value.replace(/\\D/g, '').slice(0, 6)),\n              placeholder: \"Enter 6-digit code\",\n              className: \"verification-input\",\n              maxLength: \"6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"step-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setStep(1),\n          className: \"btn btn-secondary\",\n          children: \"Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: verify2FASetup,\n          className: \"btn btn-primary\",\n          disabled: verifying || verificationCode.length !== 6,\n          children: verifying ? 'Verifying...' : 'Verify & Complete'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 17\n    }, this), step === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"setup-step\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"step-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 29\n          }, this), \" Step 3: Backup Codes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Save these backup codes in a secure location. You can use them if you lose access to your authenticator app.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"setup-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"backup-codes-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"backup-codes-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"backup-codes-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Your Backup Codes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => copyToClipboard(backupCodes.join('\\n'), 'backup'),\n                className: \"copy-btn\",\n                title: \"Copy all codes\",\n                children: [backupCodesCopied ? /*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 62\n                }, this) : /*#__PURE__*/_jsxDEV(FaCopy, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 76\n                }, this), \" Copy All\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"backup-codes-grid\",\n              children: backupCodes.map((code, index) => /*#__PURE__*/_jsxDEV(\"code\", {\n                className: \"backup-code\",\n                children: code\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"backup-warning\",\n            children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n              className: \"warning-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Important:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 37\n              }, this), \" Store these codes securely. Each code can only be used once. If you lose both your authenticator app and these codes, you may lose access to your account.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"step-actions\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleComplete,\n          className: \"btn btn-success\",\n          children: [/*#__PURE__*/_jsxDEV(FaCheck, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 29\n          }, this), \" Complete Setup\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 9\n  }, this);\n};\n_s(User2FASetup, \"pmghmqAD1v45BhYsJ3VnknTb1BU=\");\n_c = User2FASetup;\nexport default User2FASetup;\nvar _c;\n$RefreshReg$(_c, \"User2FASetup\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaQrcode", "FaKey", "FaCopy", "FaCheck", "FaArrowLeft", "FaShieldAlt", "FaExclamationTriangle", "FaInfoCircle", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "User2FASetup", "userId", "userEmail", "onSuccess", "onBack", "_s", "step", "setStep", "loading", "setLoading", "error", "setError", "success", "setSuccess", "secret<PERSON>ey", "set<PERSON>ec<PERSON><PERSON>ey", "qrCodeUrl", "setQrCodeUrl", "backupCodes", "setBackupCodes", "manualEntryKey", "setManual<PERSON>ntry<PERSON>ey", "verificationCode", "setVerificationCode", "verifying", "setVerifying", "copied", "setCopied", "backupCodesCopied", "setBackupCodesCopied", "initiate2FASetup", "response", "get", "data", "secret_key", "qr_code_url", "backup_codes", "manual_entry_key", "setTimeout", "message", "err", "console", "verify2FASetup", "length", "post", "verification_code", "copyToClipboard", "text", "type", "navigator", "clipboard", "writeText", "handleComplete", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "src", "alt", "title", "disabled", "htmlFor", "id", "value", "onChange", "e", "target", "replace", "slice", "placeholder", "max<PERSON><PERSON><PERSON>", "join", "map", "code", "index", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/User/User2FASetup.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { \n    FaQrc<PERSON>, \n    <PERSON>a<PERSON><PERSON>, \n    <PERSON>a<PERSON><PERSON>, \n    FaCheck, \n    FaArrowLeft, \n    FaShieldAlt,\n    FaExclamationTriangle,\n    FaInfoCircle\n} from 'react-icons/fa';\nimport './User2FASetup.css';\n\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost/FanBet247/backend';\n\nconst User2FASetup = ({ userId, userEmail, onSuccess, onBack }) => {\n    const [step, setStep] = useState(1); // 1: Setup, 2: Verify, 3: Backup Codes\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    \n    // Setup data\n    const [secretKey, setSecretKey] = useState('');\n    const [qrCodeUrl, setQrCodeUrl] = useState('');\n    const [backupCodes, setBackupCodes] = useState([]);\n    const [manualEntryKey, setManualEntryKey] = useState('');\n    \n    // Verification\n    const [verificationCode, setVerificationCode] = useState('');\n    const [verifying, setVerifying] = useState(false);\n    \n    // UI state\n    const [copied, setCopied] = useState(false);\n    const [backupCodesCopied, setBackupCodesCopied] = useState(false);\n\n    useEffect(() => {\n        initiate2FASetup();\n    }, []);\n\n    const initiate2FASetup = async () => {\n        try {\n            setLoading(true);\n            setError('');\n\n            const response = await axios.get(`${API_BASE_URL}/handlers/user_setup_2fa.php?userId=${userId}`);\n\n            if (response.data.success) {\n                setSecretKey(response.data.secret_key);\n                setQrCodeUrl(response.data.qr_code_url);\n                setBackupCodes(response.data.backup_codes);\n                setManualEntryKey(response.data.manual_entry_key);\n                setSuccess('2FA setup initiated successfully');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to initiate 2FA setup');\n            }\n        } catch (err) {\n            setError('Failed to initiate 2FA setup. Please try again.');\n            console.error('2FA setup error:', err);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const verify2FASetup = async () => {\n        if (!verificationCode || verificationCode.length !== 6) {\n            setError('Please enter a valid 6-digit code from your authenticator app');\n            return;\n        }\n\n        try {\n            setVerifying(true);\n            setError('');\n\n            const response = await axios.post(`${API_BASE_URL}/handlers/user_setup_2fa.php?userId=${userId}`, {\n                verification_code: verificationCode\n            });\n\n            if (response.data.success) {\n                setSuccess('2FA setup completed successfully!');\n                setStep(3); // Show backup codes\n            } else {\n                setError(response.data.message || 'Invalid verification code');\n            }\n        } catch (err) {\n            setError('Failed to verify 2FA setup. Please try again.');\n            console.error('2FA verification error:', err);\n        } finally {\n            setVerifying(false);\n        }\n    };\n\n    const copyToClipboard = async (text, type = 'secret') => {\n        try {\n            await navigator.clipboard.writeText(text);\n            if (type === 'secret') {\n                setCopied(true);\n                setTimeout(() => setCopied(false), 2000);\n            } else if (type === 'backup') {\n                setBackupCodesCopied(true);\n                setTimeout(() => setBackupCodesCopied(false), 2000);\n            }\n        } catch (err) {\n            console.error('Failed to copy to clipboard:', err);\n        }\n    };\n\n    const handleComplete = () => {\n        onSuccess();\n    };\n\n    if (loading) {\n        return (\n            <div className=\"user-2fa-setup\">\n                <div className=\"setup-loading\">\n                    <div className=\"loading-spinner\"></div>\n                    <p>Setting up 2FA...</p>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"user-2fa-setup\">\n            <div className=\"setup-header\">\n                <button onClick={onBack} className=\"back-btn\">\n                    <FaArrowLeft /> Back to Settings\n                </button>\n                <h1><FaShieldAlt /> Two-Factor Authentication Setup</h1>\n                <div className=\"setup-steps\">\n                    <div className={`step ${step >= 1 ? 'active' : ''}`}>1</div>\n                    <div className={`step ${step >= 2 ? 'active' : ''}`}>2</div>\n                    <div className={`step ${step >= 3 ? 'active' : ''}`}>3</div>\n                </div>\n            </div>\n\n            {error && (\n                <div className=\"setup-alert setup-alert-error\">\n                    <FaExclamationTriangle />\n                    <span>{error}</span>\n                </div>\n            )}\n\n            {success && (\n                <div className=\"setup-alert setup-alert-success\">\n                    <FaCheck />\n                    <span>{success}</span>\n                </div>\n            )}\n\n            {step === 1 && (\n                <div className=\"setup-step\">\n                    <div className=\"step-header\">\n                        <h2><FaQrcode /> Step 1: Scan QR Code</h2>\n                        <p>Use Google Authenticator or any compatible 2FA app to scan this QR code</p>\n                    </div>\n\n                    <div className=\"setup-content\">\n                        <div className=\"qr-section\">\n                            <div className=\"qr-code-container\">\n                                {qrCodeUrl && (\n                                    <img src={qrCodeUrl} alt=\"2FA QR Code\" className=\"qr-code\" />\n                                )}\n                            </div>\n                            \n                            <div className=\"manual-entry\">\n                                <h3><FaKey /> Manual Entry</h3>\n                                <p>If you can't scan the QR code, enter this key manually:</p>\n                                <div className=\"secret-key-container\">\n                                    <code className=\"secret-key\">{manualEntryKey}</code>\n                                    <button\n                                        onClick={() => copyToClipboard(manualEntryKey, 'secret')}\n                                        className=\"copy-btn\"\n                                        title=\"Copy to clipboard\"\n                                    >\n                                        {copied ? <FaCheck /> : <FaCopy />}\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div className=\"setup-info\">\n                            <div className=\"info-card\">\n                                <FaInfoCircle className=\"info-icon\" />\n                                <div>\n                                    <h4>Setup Instructions:</h4>\n                                    <ol>\n                                        <li>Download Google Authenticator or similar app</li>\n                                        <li>Scan the QR code or enter the key manually</li>\n                                        <li>Your app will generate 6-digit codes</li>\n                                        <li>Enter a code in the next step to verify</li>\n                                    </ol>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                    <div className=\"step-actions\">\n                        <button\n                            onClick={() => setStep(2)}\n                            className=\"btn btn-primary\"\n                            disabled={!qrCodeUrl}\n                        >\n                            Next: Verify Setup\n                        </button>\n                    </div>\n                </div>\n            )}\n\n            {step === 2 && (\n                <div className=\"setup-step\">\n                    <div className=\"step-header\">\n                        <h2><FaCheck /> Step 2: Verify Setup</h2>\n                        <p>Enter the 6-digit code from your authenticator app to verify the setup</p>\n                    </div>\n\n                    <div className=\"setup-content\">\n                        <div className=\"verification-form\">\n                            <div className=\"form-group\">\n                                <label htmlFor=\"verificationCode\">Verification Code</label>\n                                <input\n                                    type=\"text\"\n                                    id=\"verificationCode\"\n                                    value={verificationCode}\n                                    onChange={(e) => setVerificationCode(e.target.value.replace(/\\D/g, '').slice(0, 6))}\n                                    placeholder=\"Enter 6-digit code\"\n                                    className=\"verification-input\"\n                                    maxLength=\"6\"\n                                />\n                            </div>\n                        </div>\n                    </div>\n\n                    <div className=\"step-actions\">\n                        <button\n                            onClick={() => setStep(1)}\n                            className=\"btn btn-secondary\"\n                        >\n                            Back\n                        </button>\n                        <button\n                            onClick={verify2FASetup}\n                            className=\"btn btn-primary\"\n                            disabled={verifying || verificationCode.length !== 6}\n                        >\n                            {verifying ? 'Verifying...' : 'Verify & Complete'}\n                        </button>\n                    </div>\n                </div>\n            )}\n\n            {step === 3 && (\n                <div className=\"setup-step\">\n                    <div className=\"step-header\">\n                        <h2><FaShieldAlt /> Step 3: Backup Codes</h2>\n                        <p>Save these backup codes in a secure location. You can use them if you lose access to your authenticator app.</p>\n                    </div>\n\n                    <div className=\"setup-content\">\n                        <div className=\"backup-codes-section\">\n                            <div className=\"backup-codes-container\">\n                                <div className=\"backup-codes-header\">\n                                    <h3>Your Backup Codes</h3>\n                                    <button\n                                        onClick={() => copyToClipboard(backupCodes.join('\\n'), 'backup')}\n                                        className=\"copy-btn\"\n                                        title=\"Copy all codes\"\n                                    >\n                                        {backupCodesCopied ? <FaCheck /> : <FaCopy />} Copy All\n                                    </button>\n                                </div>\n                                <div className=\"backup-codes-grid\">\n                                    {backupCodes.map((code, index) => (\n                                        <code key={index} className=\"backup-code\">{code}</code>\n                                    ))}\n                                </div>\n                            </div>\n\n                            <div className=\"backup-warning\">\n                                <FaExclamationTriangle className=\"warning-icon\" />\n                                <div>\n                                    <strong>Important:</strong> Store these codes securely. Each code can only be used once. \n                                    If you lose both your authenticator app and these codes, you may lose access to your account.\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                    <div className=\"step-actions\">\n                        <button\n                            onClick={handleComplete}\n                            className=\"btn btn-success\"\n                        >\n                            <FaCheck /> Complete Setup\n                        </button>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default User2FASetup;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACIC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,WAAW,EACXC,WAAW,EACXC,qBAAqB,EACrBC,YAAY,QACT,gBAAgB;AACvB,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,oCAAoC;AAE/F,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEC,SAAS;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAAC2C,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAEjEC,SAAS,CAAC,MAAM;IACZ8C,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACArB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMoB,QAAQ,GAAG,MAAM9C,KAAK,CAAC+C,GAAG,CAAC,GAAGpC,YAAY,uCAAuCK,MAAM,EAAE,CAAC;MAEhG,IAAI8B,QAAQ,CAACE,IAAI,CAACrB,OAAO,EAAE;QACvBG,YAAY,CAACgB,QAAQ,CAACE,IAAI,CAACC,UAAU,CAAC;QACtCjB,YAAY,CAACc,QAAQ,CAACE,IAAI,CAACE,WAAW,CAAC;QACvChB,cAAc,CAACY,QAAQ,CAACE,IAAI,CAACG,YAAY,CAAC;QAC1Cf,iBAAiB,CAACU,QAAQ,CAACE,IAAI,CAACI,gBAAgB,CAAC;QACjDxB,UAAU,CAAC,kCAAkC,CAAC;QAC9CyB,UAAU,CAAC,MAAMzB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAACoB,QAAQ,CAACE,IAAI,CAACM,OAAO,IAAI,8BAA8B,CAAC;MACrE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACV7B,QAAQ,CAAC,iDAAiD,CAAC;MAC3D8B,OAAO,CAAC/B,KAAK,CAAC,kBAAkB,EAAE8B,GAAG,CAAC;IAC1C,CAAC,SAAS;MACN/B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMiC,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACpB,gBAAgB,IAAIA,gBAAgB,CAACqB,MAAM,KAAK,CAAC,EAAE;MACpDhC,QAAQ,CAAC,+DAA+D,CAAC;MACzE;IACJ;IAEA,IAAI;MACAc,YAAY,CAAC,IAAI,CAAC;MAClBd,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMoB,QAAQ,GAAG,MAAM9C,KAAK,CAAC2D,IAAI,CAAC,GAAGhD,YAAY,uCAAuCK,MAAM,EAAE,EAAE;QAC9F4C,iBAAiB,EAAEvB;MACvB,CAAC,CAAC;MAEF,IAAIS,QAAQ,CAACE,IAAI,CAACrB,OAAO,EAAE;QACvBC,UAAU,CAAC,mCAAmC,CAAC;QAC/CN,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACHI,QAAQ,CAACoB,QAAQ,CAACE,IAAI,CAACM,OAAO,IAAI,2BAA2B,CAAC;MAClE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACV7B,QAAQ,CAAC,+CAA+C,CAAC;MACzD8B,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAE8B,GAAG,CAAC;IACjD,CAAC,SAAS;MACNf,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMqB,eAAe,GAAG,MAAAA,CAAOC,IAAI,EAAEC,IAAI,GAAG,QAAQ,KAAK;IACrD,IAAI;MACA,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACJ,IAAI,CAAC;MACzC,IAAIC,IAAI,KAAK,QAAQ,EAAE;QACnBrB,SAAS,CAAC,IAAI,CAAC;QACfW,UAAU,CAAC,MAAMX,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAC5C,CAAC,MAAM,IAAIqB,IAAI,KAAK,QAAQ,EAAE;QAC1BnB,oBAAoB,CAAC,IAAI,CAAC;QAC1BS,UAAU,CAAC,MAAMT,oBAAoB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MACvD;IACJ,CAAC,CAAC,OAAOW,GAAG,EAAE;MACVC,OAAO,CAAC/B,KAAK,CAAC,8BAA8B,EAAE8B,GAAG,CAAC;IACtD;EACJ,CAAC;EAED,MAAMY,cAAc,GAAGA,CAAA,KAAM;IACzBjD,SAAS,CAAC,CAAC;EACf,CAAC;EAED,IAAIK,OAAO,EAAE;IACT,oBACIb,OAAA;MAAK0D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC3B3D,OAAA;QAAK0D,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1B3D,OAAA;UAAK0D,SAAS,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC/D,OAAA;UAAA2D,QAAA,EAAG;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACI/D,OAAA;IAAK0D,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3B3D,OAAA;MAAK0D,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACzB3D,OAAA;QAAQgE,OAAO,EAAEvD,MAAO;QAACiD,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACzC3D,OAAA,CAACL,WAAW;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBACnB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/D,OAAA;QAAA2D,QAAA,gBAAI3D,OAAA,CAACJ,WAAW;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oCAAgC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxD/D,OAAA;QAAK0D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB3D,OAAA;UAAK0D,SAAS,EAAE,QAAQ/C,IAAI,IAAI,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAgD,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5D/D,OAAA;UAAK0D,SAAS,EAAE,QAAQ/C,IAAI,IAAI,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAgD,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5D/D,OAAA;UAAK0D,SAAS,EAAE,QAAQ/C,IAAI,IAAI,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAgD,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELhD,KAAK,iBACFf,OAAA;MAAK0D,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC1C3D,OAAA,CAACH,qBAAqB;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzB/D,OAAA;QAAA2D,QAAA,EAAO5C;MAAK;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACR,EAEA9C,OAAO,iBACJjB,OAAA;MAAK0D,SAAS,EAAC,iCAAiC;MAAAC,QAAA,gBAC5C3D,OAAA,CAACN,OAAO;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACX/D,OAAA;QAAA2D,QAAA,EAAO1C;MAAO;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACR,EAEApD,IAAI,KAAK,CAAC,iBACPX,OAAA;MAAK0D,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACvB3D,OAAA;QAAK0D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB3D,OAAA;UAAA2D,QAAA,gBAAI3D,OAAA,CAACT,QAAQ;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yBAAqB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1C/D,OAAA;UAAA2D,QAAA,EAAG;QAAuE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1B3D,OAAA;UAAK0D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB3D,OAAA;YAAK0D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC7BtC,SAAS,iBACNrB,OAAA;cAAKiE,GAAG,EAAE5C,SAAU;cAAC6C,GAAG,EAAC,aAAa;cAACR,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC/D;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEN/D,OAAA;YAAK0D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB3D,OAAA;cAAA2D,QAAA,gBAAI3D,OAAA,CAACR,KAAK;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAAa;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/B/D,OAAA;cAAA2D,QAAA,EAAG;YAAuD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9D/D,OAAA;cAAK0D,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjC3D,OAAA;gBAAM0D,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAElC;cAAc;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpD/D,OAAA;gBACIgE,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC1B,cAAc,EAAE,QAAQ,CAAE;gBACzDiC,SAAS,EAAC,UAAU;gBACpBS,KAAK,EAAC,mBAAmB;gBAAAR,QAAA,EAExB5B,MAAM,gBAAG/B,OAAA,CAACN,OAAO;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG/D,OAAA,CAACP,MAAM;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/D,OAAA;UAAK0D,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvB3D,OAAA;YAAK0D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB3D,OAAA,CAACF,YAAY;cAAC4D,SAAS,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtC/D,OAAA;cAAA2D,QAAA,gBACI3D,OAAA;gBAAA2D,QAAA,EAAI;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5B/D,OAAA;gBAAA2D,QAAA,gBACI3D,OAAA;kBAAA2D,QAAA,EAAI;gBAA4C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrD/D,OAAA;kBAAA2D,QAAA,EAAI;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnD/D,OAAA;kBAAA2D,QAAA,EAAI;gBAAoC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7C/D,OAAA;kBAAA2D,QAAA,EAAI;gBAAuC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,cAAc;QAAAC,QAAA,eACzB3D,OAAA;UACIgE,OAAO,EAAEA,CAAA,KAAMpD,OAAO,CAAC,CAAC,CAAE;UAC1B8C,SAAS,EAAC,iBAAiB;UAC3BU,QAAQ,EAAE,CAAC/C,SAAU;UAAAsC,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEApD,IAAI,KAAK,CAAC,iBACPX,OAAA;MAAK0D,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACvB3D,OAAA;QAAK0D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB3D,OAAA;UAAA2D,QAAA,gBAAI3D,OAAA,CAACN,OAAO;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yBAAqB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzC/D,OAAA;UAAA2D,QAAA,EAAG;QAAsE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1B3D,OAAA;UAAK0D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAC9B3D,OAAA;YAAK0D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvB3D,OAAA;cAAOqE,OAAO,EAAC,kBAAkB;cAAAV,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3D/D,OAAA;cACIqD,IAAI,EAAC,MAAM;cACXiB,EAAE,EAAC,kBAAkB;cACrBC,KAAK,EAAE5C,gBAAiB;cACxB6C,QAAQ,EAAGC,CAAC,IAAK7C,mBAAmB,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK,CAACI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE;cACpFC,WAAW,EAAC,oBAAoB;cAChCnB,SAAS,EAAC,oBAAoB;cAC9BoB,SAAS,EAAC;YAAG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzB3D,OAAA;UACIgE,OAAO,EAAEA,CAAA,KAAMpD,OAAO,CAAC,CAAC,CAAE;UAC1B8C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAChC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/D,OAAA;UACIgE,OAAO,EAAEjB,cAAe;UACxBW,SAAS,EAAC,iBAAiB;UAC3BU,QAAQ,EAAEvC,SAAS,IAAIF,gBAAgB,CAACqB,MAAM,KAAK,CAAE;UAAAW,QAAA,EAEpD9B,SAAS,GAAG,cAAc,GAAG;QAAmB;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEApD,IAAI,KAAK,CAAC,iBACPX,OAAA;MAAK0D,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACvB3D,OAAA;QAAK0D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB3D,OAAA;UAAA2D,QAAA,gBAAI3D,OAAA,CAACJ,WAAW;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yBAAqB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7C/D,OAAA;UAAA2D,QAAA,EAAG;QAA4G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClH,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1B3D,OAAA;UAAK0D,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACjC3D,OAAA;YAAK0D,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACnC3D,OAAA;cAAK0D,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAChC3D,OAAA;gBAAA2D,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1B/D,OAAA;gBACIgE,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC5B,WAAW,CAACwD,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAE;gBACjErB,SAAS,EAAC,UAAU;gBACpBS,KAAK,EAAC,gBAAgB;gBAAAR,QAAA,GAErB1B,iBAAiB,gBAAGjC,OAAA,CAACN,OAAO;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG/D,OAAA,CAACP,MAAM;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAAC,WAClD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACN/D,OAAA;cAAK0D,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAC7BpC,WAAW,CAACyD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBlF,OAAA;gBAAkB0D,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEsB;cAAI,GAApCC,KAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAsC,CACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN/D,OAAA;YAAK0D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B3D,OAAA,CAACH,qBAAqB;cAAC6D,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClD/D,OAAA;cAAA2D,QAAA,gBACI3D,OAAA;gBAAA2D,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,+JAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,cAAc;QAAAC,QAAA,eACzB3D,OAAA;UACIgE,OAAO,EAAEP,cAAe;UACxBC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAE3B3D,OAAA,CAACN,OAAO;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBACf;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACrD,EAAA,CA7RIL,YAAY;AAAA8E,EAAA,GAAZ9E,YAAY;AA+RlB,eAAeA,YAAY;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}