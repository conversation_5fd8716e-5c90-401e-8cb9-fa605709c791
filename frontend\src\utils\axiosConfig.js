import axios from 'axios';
import { API_BASE_URL } from '../config';

// Create axios instance with consistent configuration
const axiosInstance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000, // 30 second timeout
    withCredentials: true,
    headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Accept': 'application/json'
    }
});

// Request interceptor for authentication and URL normalization
axiosInstance.interceptors.request.use(
    (config) => {
        // Normalize URL to prevent duplicate handlers
        if (config.url && config.url.includes('/handlers/handlers/')) {
            config.url = config.url.replace('/handlers/handlers/', '/handlers/');
        }

        // Ensure handlers prefix for API endpoints - but only if URL doesn't already include handlers
        if (config.url && !config.url.startsWith('/handlers/') && !config.url.startsWith('http') && !config.url.includes('/handlers/')) {
            config.url = `/handlers/${config.url}`;
        }

        // Add authentication token
        if (!config.headers.Authorization) {
            const token = localStorage.getItem('userToken');
            const adminId = localStorage.getItem('adminId');

            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            } else if (adminId) {
                config.headers['X-Admin-ID'] = adminId;
            }
        }

        // Log request in development
        if (process.env.NODE_ENV === 'development') {
            console.log(`🔄 ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);
        }

        return config;
    },
    (error) => {
        console.error('❌ Request interceptor error:', error);
        return Promise.reject(error);
    }
);

// Response interceptor for consistent error handling
axiosInstance.interceptors.response.use(
    (response) => {
        // Log successful responses in development
        if (process.env.NODE_ENV === 'development') {
            console.log(`✅ ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);
        }
        return response;
    },
    (error) => {
        const { response, config } = error;

        // Log error details
        console.error(`❌ ${config?.method?.toUpperCase()} ${config?.url} - ${response?.status || 'Network Error'}`);

        // Handle authentication errors
        if (response?.status === 401 && !config?.url?.includes('join_league.php')) {
            handleAuthError();
        }

        // Handle network errors
        if (!response) {
            error.message = 'Network error - please check your connection';
        }

        return Promise.reject(error);
    }
);

// Authentication error handler
const handleAuthError = () => {
    const adminId = localStorage.getItem('adminId');
    const userToken = localStorage.getItem('userToken');

    if (adminId) {
        // Admin session expired
        localStorage.removeItem('adminId');
        localStorage.removeItem('adminUsername');
        localStorage.removeItem('adminRole');
        window.location.href = '/admin/login';
    } else if (userToken) {
        // User session expired
        localStorage.removeItem('userToken');
        localStorage.removeItem('userId');
        window.location.href = '/login';
    }
};

export default axiosInstance;
